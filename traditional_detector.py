"""
基于传统计算机视觉的车辆编号检测器
Traditional Computer Vision Vehicle Number Detector
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Tuple
import pytesseract
from PIL import Image
import re
import time
from loguru import logger


class TraditionalVehicleDetector:
    """传统计算机视觉车辆编号检测器"""
    
    def __init__(self):
        """初始化检测器"""
        self.min_area = 1000  # 最小检测区域面积
        self.max_area = 50000  # 最大检测区域面积
        
    def detect_number_regions(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        检测可能的数字区域
        
        Args:
            image: 输入图像
            
        Returns:
            检测到的区域列表
        """
        results = []
        
        # 方法1: 检测圆形区域（圆形标识）
        circles = self._detect_circular_badges(image)
        results.extend(circles)
        
        # 方法2: 检测文字区域（直接喷涂）
        text_regions = self._detect_text_regions(image)
        results.extend(text_regions)
        
        # 方法3: 检测蓝色区域上的白色文字
        blue_regions = self._detect_blue_background_text(image)
        results.extend(blue_regions)
        
        return self._filter_and_merge_regions(results)
    
    def _detect_circular_badges(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """检测圆形标识"""
        results = []
        
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 使用HoughCircles检测圆形
        circles = cv2.HoughCircles(
            gray,
            cv2.HOUGH_GRADIENT,
            dp=1,
            minDist=50,
            param1=50,
            param2=30,
            minRadius=20,
            maxRadius=80
        )
        
        if circles is not None:
            circles = np.round(circles[0, :]).astype("int")
            
            for (x, y, r) in circles:
                # 扩展检测区域
                margin = int(r * 0.3)
                x1 = max(0, x - r - margin)
                y1 = max(0, y - r - margin)
                x2 = min(image.shape[1], x + r + margin)
                y2 = min(image.shape[0], y + r + margin)
                
                results.append({
                    'bbox': [x1, y1, x2, y2],
                    'type': 'circular',
                    'confidence': 0.8,
                    'center': (x, y),
                    'radius': r
                })
        
        return results
    
    def _detect_text_regions(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """检测文字区域"""
        results = []
        
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 自适应二值化
        binary = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # 形态学操作连接文字
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (10, 3))
        connected = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(connected, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.min_area < area < self.max_area:
                x, y, w, h = cv2.boundingRect(contour)
                
                # 检查宽高比
                aspect_ratio = w / h if h > 0 else 0
                if 0.5 < aspect_ratio < 8:  # 数字的合理宽高比
                    results.append({
                        'bbox': [x, y, x + w, y + h],
                        'type': 'text',
                        'confidence': 0.6,
                        'area': area,
                        'aspect_ratio': aspect_ratio
                    })
        
        return results
    
    def _detect_blue_background_text(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """检测蓝色背景上的白色文字"""
        results = []
        
        # 转换到HSV颜色空间
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 定义蓝色范围
        lower_blue = np.array([100, 50, 50])
        upper_blue = np.array([130, 255, 255])
        
        # 创建蓝色掩码
        blue_mask = cv2.inRange(hsv, lower_blue, upper_blue)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        blue_mask = cv2.morphologyEx(blue_mask, cv2.MORPH_CLOSE, kernel)
        
        # 查找蓝色区域轮廓
        contours, _ = cv2.findContours(blue_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 5000:  # 足够大的蓝色区域
                x, y, w, h = cv2.boundingRect(contour)
                
                results.append({
                    'bbox': [x, y, x + w, y + h],
                    'type': 'blue_background',
                    'confidence': 0.7,
                    'area': area
                })
        
        return results
    
    def _filter_and_merge_regions(self, regions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤和合并重叠区域"""
        if not regions:
            return []
        
        # 按置信度排序
        regions.sort(key=lambda x: x['confidence'], reverse=True)
        
        # 简单的非最大抑制
        filtered = []
        for region in regions:
            overlap = False
            for existing in filtered:
                if self._calculate_iou(region['bbox'], existing['bbox']) > 0.3:
                    overlap = True
                    break
            
            if not overlap:
                filtered.append(region)
        
        return filtered
    
    def _calculate_iou(self, box1: List[int], box2: List[int]) -> float:
        """计算两个边界框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # 计算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    def recognize_numbers(self, image: np.ndarray, regions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """在检测到的区域中识别数字"""
        results = []
        
        for region in regions:
            x1, y1, x2, y2 = region['bbox']
            roi = image[y1:y2, x1:x2]
            
            if roi.size == 0:
                continue
            
            # 预处理ROI
            processed_roi = self._preprocess_roi(roi, region['type'])
            
            # 使用pytesseract识别
            try:
                # 配置tesseract只识别数字
                config = '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789'
                text = pytesseract.image_to_string(processed_roi, config=config).strip()
                
                # 验证结果
                if text and text.isdigit():
                    number = int(text)
                    if 1 <= number <= 1000:  # 验证范围
                        results.append({
                            'number': number,
                            'text': text,
                            'bbox': region['bbox'],
                            'type': region['type'],
                            'confidence': region['confidence'] * 0.9,  # 稍微降低置信度
                            'roi_processed': processed_roi
                        })
            except Exception as e:
                logger.debug(f"OCR识别失败: {e}")
                continue
        
        # 按置信度排序
        results.sort(key=lambda x: x['confidence'], reverse=True)
        return results
    
    def _preprocess_roi(self, roi: np.ndarray, region_type: str) -> np.ndarray:
        """预处理感兴趣区域"""
        # 调整大小以提高OCR效果
        height, width = roi.shape[:2]
        if height < 50:
            scale = 50 / height
            new_width = int(width * scale)
            roi = cv2.resize(roi, (new_width, 50))
        
        # 转换为灰度图
        if len(roi.shape) == 3:
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        else:
            gray = roi.copy()
        
        # 根据区域类型选择不同的预处理
        if region_type == 'blue_background':
            # 对于蓝色背景，直接二值化
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        elif region_type == 'circular':
            # 对于圆形标识，增强对比度
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(gray)
            _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        else:
            # 对于普通文字，自适应二值化
            binary = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
        
        # 去噪
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return binary
    
    def detect_and_recognize(self, image: np.ndarray) -> Dict[str, Any]:
        """检测并识别车辆编号"""
        start_time = time.time()
        
        try:
            # 检测数字区域
            regions = self.detect_number_regions(image)
            
            if not regions:
                return {
                    'success': False,
                    'error': '未检测到可能的数字区域',
                    'processing_time': time.time() - start_time
                }
            
            # 识别数字
            results = self.recognize_numbers(image, regions)
            
            if not results:
                return {
                    'success': False,
                    'error': '在检测区域中未识别到有效数字',
                    'processing_time': time.time() - start_time,
                    'detected_regions': len(regions)
                }
            
            # 返回最佳结果
            best_result = results[0]
            
            return {
                'success': True,
                'number': best_result['number'],
                'confidence': best_result['confidence'],
                'text': best_result['text'],
                'bbox': best_result['bbox'],
                'type': best_result['type'],
                'processing_time': time.time() - start_time,
                'all_results': results
            }
            
        except Exception as e:
            logger.error(f"检测识别过程出错: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time
            }
    
    def visualize_result(self, image: np.ndarray, result: Dict[str, Any]) -> np.ndarray:
        """可视化识别结果"""
        vis_image = image.copy()
        
        if result['success']:
            # 绘制检测框
            x1, y1, x2, y2 = result['bbox']
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 显示识别结果
            label = f"Number: {result['number']} ({result['confidence']:.2f})"
            cv2.putText(vis_image, label, (x1, y1 - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 显示类型
            type_label = f"Type: {result['type']}"
            cv2.putText(vis_image, type_label, (x1, y2 + 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
        return vis_image


# 使用示例函数
def test_traditional_detector():
    """测试传统检测器"""
    detector = TraditionalVehicleDetector()
    
    # 测试图片路径列表
    test_images = [
        "data/images/train/cq1_IP监控点2_cq1_20250711095148_27821307.jpg",  # 233
        "data/images/train/cq1_IP监控点2_cq1_20250711100406_758858889.jpg",  # 119
        "data/images/train/cq3_IP监控点3_cq3_20250711105646_1217638354.jpg",  # 256
        "data/images/train/cq5_IP监控点3_cq5_20250711103617_126309802.jpg",  # 188
    ]
    
    for img_path in test_images:
        print(f"\\n测试图片: {img_path}")
        
        # 读取图片
        image = cv2.imread(img_path)
        if image is None:
            print(f"无法读取图片: {img_path}")
            continue
        
        # 检测和识别
        result = detector.detect_and_recognize(image)
        
        # 显示结果
        if result['success']:
            print(f"✅ 识别成功: {result['number']}")
            print(f"   置信度: {result['confidence']:.3f}")
            print(f"   类型: {result['type']}")
            print(f"   处理时间: {result['processing_time']:.3f}秒")
        else:
            print(f"❌ 识别失败: {result['error']}")
            print(f"   处理时间: {result['processing_time']:.3f}秒")
        
        # 保存可视化结果
        vis_image = detector.visualize_result(image, result)
        output_path = f"traditional_result_{img_path.split('/')[-1]}"
        cv2.imwrite(output_path, vis_image)
        print(f"   可视化结果保存到: {output_path}")


if __name__ == "__main__":
    test_traditional_detector()