# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Vehicle Number OCR System (车辆自编码识别系统) that uses a two-stage approach to identify vehicle numbers painted on truck doors:
1. **YOLO11 Object Detection** - Locates number regions on vehicle doors
2. **PaddleOCR Text Recognition** - Extracts and recognizes numbers in the 1-1000 range

The system is designed for industrial vehicle management in construction sites and concrete plants.

## Development Environment Setup

### Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Or use the automated installer
./install.sh
```

### Environment Requirements
- Python 3.12+ 
- PyTorch 2.0+
- CUDA 11.8+ (optional, for GPU acceleration)
- Conda environment `env-py312` may be used

## Running the System

### Command Line Interface
```bash
# Recognize single image
python -m src.cli recognize --image path/to/vehicle.jpg

# Batch processing
python -m src.cli batch --input-dir images/ --output results.json

# Show system information
python -m src.cli info

# Create configuration file
python -m src.cli create-config --output my_config.yaml
```

### Python API
```python
from src.vehicle_ocr import VehicleOCR

# Initialize with default or custom config
ocr = VehicleOCR()  # or VehicleOCR(config)

# Single image recognition
result = ocr.recognize("image.jpg", return_details=True)

# Batch processing
results = ocr.batch_recognize(["img1.jpg", "img2.jpg"])
```

### Quick Demo
```bash
python quick_start.py
```

## Testing and Code Quality

### Run Tests
```bash
# All tests
python -m pytest tests/ -v

# Specific test file
python -m pytest tests/test_vehicle_ocr.py -v

# Single test
python -m pytest tests/test_vehicle_ocr.py::test_specific_function -v
```

### Code Quality Tools
Available tools (configurations may need to be added):
```bash
# Code formatting
black src/ tests/

# Linting
flake8 src/ tests/

# Type checking
mypy src/
```

## Core Architecture

### Main Components
- **VehicleOCR** (`src/vehicle_ocr.py`) - Main orchestration class that coordinates detection, OCR, and validation
- **YOLODetector** (`src/models/yolo_detector.py`) - YOLO11-based object detection for number regions
- **OCRRecognizer** (`src/models/ocr_recognizer.py`) - PaddleOCR-based text recognition
- **ResultValidator** (`src/utils/validator.py`) - Validates results are in 1-1000 range with confidence filtering
- **ImageProcessor** (`src/utils/image_processor.py`) - Image preprocessing and enhancement
- **Config** (`src/utils/config.py`) - Configuration management system

### Processing Pipeline
```
Input Image → Image Preprocessing → YOLO11 Detection → Crop Regions → 
OCR Recognition → Result Validation → Best Result Selection → Output
```

### Configuration System
Main config file: `config.yaml` with sections:
- **yolo**: Model selection (yolo11n.pt to yolo11x.pt), confidence thresholds, device settings
- **ocr**: PaddleOCR settings, language, GPU usage
- **image_process**: Size normalization, enhancement, denoising
- **validation**: Number range (1-1000), confidence filtering, duplicate removal

## Key Development Patterns

### Error Handling
All main methods return standardized result dictionaries with `success`, `error`, `confidence`, and `processing_time` fields.

### Configuration Updates
Components support runtime configuration updates via `update_config(**kwargs)` methods.

### Visualization
All components provide visualization methods for debugging:
- `visualize_detections()` for YOLO results
- `visualize_results()` for OCR results  
- `visualize_result()` for complete pipeline

### Logging
Uses `loguru` for structured logging throughout the codebase.

## Sample Data
Test images are available in `data/images/` showing real construction vehicle scenarios with numbers like 233, 119, and 256.

## Common Issues
- Ensure proper conda environment activation before running
- GPU acceleration requires CUDA installation
- PaddleOCR models download automatically on first run
- YOLO11 models download from Ultralytics hub on first use