# YOLO11训练配置文件
# YOLO11 Training Configuration for Vehicle Number Detection

path: ./data  # 数据集根路径
train: images/train  # 训练图片路径
val: images/val      # 验证图片路径

# 类别定义
nc: 1  # 类别数量
names: ['vehicle_number']  # 类别名称

# 训练参数
epochs: 200          # 训练轮数
patience: 50         # 早停耐心值
batch: 16            # 批次大小
imgsz: 640          # 输入图像尺寸
save_period: 10      # 模型保存间隔

# 数据增强
hsv_h: 0.015        # 色调增强
hsv_s: 0.7          # 饱和度增强  
hsv_v: 0.4          # 明度增强
degrees: 15.0       # 旋转角度
translate: 0.1      # 平移
scale: 0.2          # 缩放
shear: 0.0          # 错切
perspective: 0.0    # 透视变换
flipud: 0.0         # 上下翻转
fliplr: 0.5         # 左右翻转
mosaic: 1.0         # 马赛克增强
mixup: 0.0          # 混合增强

# 优化器参数
lr0: 0.01           # 初始学习率
lrf: 0.1            # 最终学习率
momentum: 0.937     # 动量
weight_decay: 0.0005 # 权重衰减
warmup_epochs: 3    # 预热轮数
warmup_momentum: 0.8 # 预热动量
warmup_bias_lr: 0.1 # 预热偏置学习率

# 损失权重
box: 7.5            # 边界框损失权重
cls: 0.5            # 分类损失权重
dfl: 1.5            # DFL损失权重

# NMS参数  
iou: 0.7            # NMS IoU阈值
conf: 0.25          # 置信度阈值

# 模型配置
model: yolo11n.pt   # 预训练模型 (nano版本，速度快)
# 可选模型: yolo11s.pt, yolo11m.pt, yolo11l.pt, yolo11x.pt

# 设备配置
device: ''          # 自动选择设备 (CPU/GPU)
workers: 8          # 数据加载线程数
project: runs/detect # 项目目录
name: vehicle_number # 实验名称