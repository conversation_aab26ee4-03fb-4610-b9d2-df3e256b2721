# YOLO11车辆编号检测训练数据集
# Vehicle Number Detection Training Dataset

## 数据集概览
- **总图片数**: 19张
- **有效标注**: 18张（1张无可见编号）
- **类别数**: 1个（vehicle_number）
- **标注格式**: YOLO格式 (class_id x_center y_center width height)

## 完整图片列表及编号

### 原始数据集 (11张)
1. cq1_IP监控点2_cq1_20250711095148_27821307.jpg - **233** (圆形标识)
2. cq1_IP监控点2_cq1_20250711100406_758858889.jpg - **119** (圆形标识+2021)
3. cq1_IP监控点2_cq1_20250711103938_355489891.jpg - **28** (直接喷涂)
4. cq2线_IP监控点8_cq2线_20250711100426_772235871.jpg - **113** (圆形标识)
5. cq2线_IP监控点8_cq2线_20250711110705_1735938766.jpg - **23** (直接喷涂)
6. cq3_IP监控点3_cq3_20250711105646_1217638354.jpg - **256** (白字蓝背景)
7. cq3_IP监控点3_cq3_20250711110005_1304061496.jpg - **164** (破损圆形标识)
8. cq5_IP监控点3_cq5_20250711103617_126309802.jpg - **188** (直接喷涂大字)
9. cq5_IP监控点3_cq5_20250711105620_1214431030.jpg - **268** (白字蓝背景)
10. cq5_IP监控点3_cq5_20250711105923_1293100510.jpg - **268** (同车不同角度)
11. cq5_IP监控点3_cq5_20250711110856_1733553519.jpg - **179** (直接喷涂大字)

### 新增数据集 (8张)
12. cq1_IP监控点2_cq1_20250711140230_24935789.jpg - **28** (直接喷涂)
13. cq2线_IP监控点8_cq2线_20250711140234_29533492.jpg - **177** (直接喷涂)
14. cq2线_IP监控点8_cq2线_20250711140949_286983517.jpg - **111** (圆形标识)
15. cq3_IP监控点3_cq3_20250711140542_62216267.jpg - **276** (白字蓝背景)
16. cq5_IP监控点3_cq5_20250711140517_59723038.jpg - **183** (直接喷涂)
17. cq5_IP监控点3_cq5_20250711141131_279247065.jpg - **191** (直接喷涂)
18. cq6_IP监控点1_cq6_20250711135705_71257445.jpg - **150** (圆形标识+2021)
19. cq6_IP监控点1_cq6_20250711140201_253640963.jpg - **无编号** (遮挡/不可见)

## 编号类型统计

### 圆形标识 (7张)
- 233, 119, 113, 164(破损), 111, 150
- 特征: 蓝色/青色圆形背景，白色数字
- 部分带有年份标识(2021)
- 存在破损情况需要特别处理

### 直接喷涂 (8张)  
- 28, 23, 188, 179, 177, 183, 191
- 特征: 黑色字体直接喷涂在白色车门上
- 字体较大，位置多样

### 对比色喷涂 (3张)
- 256, 268, 276  
- 特征: 白色字体在蓝色车门上
- 对比度高，识别相对容易

## 训练建议

### 数据增强策略
1. **旋转**: ±15度（考虑拍摄角度变化）
2. **缩放**: 0.8-1.2倍（考虑距离变化）
3. **亮度调整**: ±20%（适应不同光照）
4. **对比度调整**: ±15%（增强鲁棒性）
5. **添加噪声**: 模拟真实监控环境

### 难例挖掘重点
1. **破损标识**: 164号的圆形标识破损情况
2. **部分遮挡**: 人员或物体遮挡编号
3. **光照变化**: 不同时间和天气条件
4. **角度变化**: 不同监控点的拍摄角度

### 模型训练参数建议
- **输入尺寸**: 640x640 (标准YOLO输入)
- **批次大小**: 16-32 (根据GPU内存调整)
- **学习率**: 0.01 (初始)
- **训练轮数**: 100-200 epochs
- **验证集比例**: 20% (约4张图片)

## 质量控制

### 标注质量检查
- 所有边界框覆盖完整编号区域
- 坐标归一化正确 (0-1范围)
- 特殊情况(破损、遮挡)有详细说明

### 验证策略
- 不同编号类型均匀分布
- 包含各种拍摄条件和角度
- 确保模型泛化能力