# YOLO11车辆编号检测数据集配置文件
# Vehicle Number Detection Dataset Configuration

path: /Users/<USER>/zjssnf/truck_and_concrete_detection/truck_number_ocr/data
train: train/images
val: val/images

# 类别定义 - 车辆编号
nc: 1
names: ['vehicle_number']

# 数据集信息
dataset_info:
  name: "Vehicle Number Detection"
  description: "车辆编号检测数据集，包含圆形标识和直接喷涂两种类型"
  version: "1.0"
  total_images: 17
  
# 标注统计
annotation_stats:
  total_annotations: 17
  class_distribution:
    vehicle_number: 17
  
# 编号类型统计
number_types:
  circular_badge: 8  # 圆形标识（蓝色/青色背景）
  direct_spray: 9    # 直接喷涂（黑色/白色字体）
  
# 特殊情况
special_cases:
  damaged_badge: 1   # 破损的圆形标识 (164)
  partial_occlusion: 2  # 部分遮挡
  
# 数字范围
number_range:
  min: 28
  max: 276
  average: 155