"""
OCR识别模块
OCR recognition module using PaddleOCR for vehicle number recognition
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union
from pathlib import Path
import re
from loguru import logger

try:
    from paddleocr import PaddleOCR
except ImportError:
    logger.warning("PaddleOCR未安装，请运行: pip install paddleocr")
    PaddleOCR = None

from ..utils.config import OCRConfig
from ..utils.validator import ResultValidator


class OCRRecognizer:
    """OCR识别器"""
    
    def __init__(self, config: Optional[OCRConfig] = None):
        """
        初始化OCR识别器
        
        Args:
            config: OCR配置
        """
        self.config = config or OCRConfig()
        self.ocr = None
        self.validator = ResultValidator()
        self._load_ocr()
    
    def _load_ocr(self):
        """加载PaddleOCR模型"""
        if PaddleOCR is None:
            raise ImportError("PaddleOCR未安装，请运行: pip install paddleocr")
        
        try:
            logger.info("正在加载PaddleOCR模型...")
            
            self.ocr = PaddleOCR(
                use_angle_cls=self.config.use_angle_cls,
                lang=self.config.lang,
                use_gpu=self.config.use_gpu,
                show_log=self.config.show_log,
                det_model_dir=self.config.det_model_dir,
                rec_model_dir=self.config.rec_model_dir,
                cls_model_dir=self.config.cls_model_dir
            )
            
            logger.info("PaddleOCR模型加载成功")
            
        except Exception as e:
            logger.error(f"加载PaddleOCR模型失败: {e}")
            raise
    
    def recognize(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        识别图像中的文字
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            识别结果列表，每个结果包含text, confidence, bbox等信息
        """
        if self.ocr is None:
            raise RuntimeError("OCR模型未加载")
        
        try:
            # 执行OCR识别
            results = self.ocr.ocr(image, cls=self.config.use_angle_cls)
            
            # 解析结果
            ocr_results = []
            if results and results[0]:
                for line in results[0]:
                    if line:
                        result = self._parse_ocr_result(line)
                        if result:
                            ocr_results.append(result)
            
            # 按置信度排序
            ocr_results.sort(key=lambda x: x['confidence'], reverse=True)
            
            logger.debug(f"OCR识别到 {len(ocr_results)} 个文本区域")
            return ocr_results
            
        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            return []
    
    def _parse_ocr_result(self, line: List) -> Optional[Dict[str, Any]]:
        """
        解析单个OCR识别结果
        
        Args:
            line: PaddleOCR返回的单行结果
            
        Returns:
            解析后的结果字典
        """
        try:
            if len(line) != 2:
                return None
            
            bbox, (text, confidence) = line
            
            # 计算边界框
            bbox_points = np.array(bbox, dtype=np.int32)
            x_coords = bbox_points[:, 0]
            y_coords = bbox_points[:, 1]
            
            x1, y1 = int(x_coords.min()), int(y_coords.min())
            x2, y2 = int(x_coords.max()), int(y_coords.max())
            
            # 计算中心点和面积
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            area = (x2 - x1) * (y2 - y1)
            
            return {
                'text': text.strip(),
                'confidence': float(confidence),
                'bbox': [x1, y1, x2, y2],
                'bbox_points': bbox_points.tolist(),
                'center': [center_x, center_y],
                'area': area,
                'width': x2 - x1,
                'height': y2 - y1
            }
            
        except Exception as e:
            logger.warning(f"解析OCR结果失败: {e}")
            return None
    
    def recognize_numbers(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        专门识别数字
        
        Args:
            image: 输入图像
            
        Returns:
            数字识别结果
        """
        # 执行OCR识别
        ocr_results = self.recognize(image)
        
        # 过滤和验证数字结果
        number_results = []
        for result in ocr_results:
            # 使用验证器验证结果
            validation = self.validator.validate_ocr_result(result)
            
            if validation.is_valid:
                result['validation'] = validation
                result['number'] = validation.number
                number_results.append(result)
        
        return number_results
    
    def recognize_region(self, image: np.ndarray, bbox: List[int]) -> List[Dict[str, Any]]:
        """
        识别指定区域的文字
        
        Args:
            image: 原始图像
            bbox: 区域边界框 [x1, y1, x2, y2]
            
        Returns:
            识别结果
        """
        # 裁剪区域
        x1, y1, x2, y2 = bbox
        h, w = image.shape[:2]
        
        # 确保坐标在图像范围内
        x1 = max(0, min(x1, w))
        y1 = max(0, min(y1, h))
        x2 = max(0, min(x2, w))
        y2 = max(0, min(y2, h))
        
        if x2 <= x1 or y2 <= y1:
            return []
        
        region = image[y1:y2, x1:x2]
        
        # 对区域进行预处理
        processed_region = self._preprocess_region(region)
        
        # 识别文字
        results = self.recognize(processed_region)
        
        # 调整坐标到原图坐标系
        for result in results:
            result['bbox'][0] += x1
            result['bbox'][1] += y1
            result['bbox'][2] += x1
            result['bbox'][3] += y1
            result['center'][0] += x1
            result['center'][1] += y1
            
            # 调整bbox_points坐标
            if 'bbox_points' in result:
                for point in result['bbox_points']:
                    point[0] += x1
                    point[1] += y1
        
        return results
    
    def _preprocess_region(self, region: np.ndarray) -> np.ndarray:
        """
        预处理OCR识别区域
        
        Args:
            region: 输入区域图像
            
        Returns:
            预处理后的图像
        """
        if region.size == 0:
            return region
        
        # 转换为灰度图
        if len(region.shape) == 3:
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
        else:
            gray = region.copy()
        
        # 增强对比度
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # 去噪
        denoised = cv2.medianBlur(enhanced, 3)
        
        # 二值化
        _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # 转换回BGR格式
        if len(region.shape) == 3:
            processed = cv2.cvtColor(processed, cv2.COLOR_GRAY2BGR)
        
        return processed
    
    def batch_recognize(self, images: List[np.ndarray]) -> List[List[Dict[str, Any]]]:
        """
        批量识别
        
        Args:
            images: 图像列表
            
        Returns:
            每张图像的识别结果列表
        """
        results = []
        for i, image in enumerate(images):
            logger.debug(f"处理第 {i+1}/{len(images)} 张图像")
            result = self.recognize_numbers(image)
            results.append(result)
        
        return results
    
    def get_best_number(self, image: np.ndarray) -> Optional[Dict[str, Any]]:
        """
        获取最佳数字识别结果
        
        Args:
            image: 输入图像
            
        Returns:
            最佳识别结果，如果没有有效结果则返回None
        """
        results = self.recognize_numbers(image)
        return self.validator.get_best_result(results)
    
    def visualize_results(self, image: np.ndarray, 
                         results: List[Dict[str, Any]]) -> np.ndarray:
        """
        可视化OCR识别结果
        
        Args:
            image: 原始图像
            results: OCR识别结果
            
        Returns:
            标注了识别结果的图像
        """
        vis_image = image.copy()
        
        for result in results:
            # 绘制边界框
            if 'bbox_points' in result:
                points = np.array(result['bbox_points'], dtype=np.int32)
                cv2.polylines(vis_image, [points], True, (0, 255, 0), 2)
            else:
                x1, y1, x2, y2 = result['bbox']
                cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 绘制文本和置信度
            text = result['text']
            confidence = result['confidence']
            x1, y1 = result['bbox'][:2]
            
            label = f"{text} ({confidence:.3f})"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            
            # 绘制标签背景
            cv2.rectangle(vis_image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            
            # 绘制标签文本
            cv2.putText(vis_image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        return vis_image
    
    def update_config(self, **kwargs):
        """
        更新配置参数
        
        Args:
            **kwargs: 要更新的配置参数
        """
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                logger.info(f"更新OCR配置: {key} = {value}")
        
        # 如果更新了关键配置，需要重新加载模型
        critical_configs = ['use_gpu', 'lang', 'det_model_dir', 'rec_model_dir', 'cls_model_dir']
        if any(key in critical_configs for key in kwargs.keys()):
            logger.info("检测到关键配置更新，重新加载OCR模型...")
            self._load_ocr()
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        return {
            'use_angle_cls': self.config.use_angle_cls,
            'lang': self.config.lang,
            'use_gpu': self.config.use_gpu,
            'show_log': self.config.show_log
        }
