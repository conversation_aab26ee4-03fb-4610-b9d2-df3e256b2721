"""
YOLO11目标检测模块
YOLO11 object detection module for vehicle number detection
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union
from pathlib import Path
import torch
from ultralytics import YOLO
from loguru import logger

from ..utils.config import YOLOConfig


class YOLODetector:
    """YOLO11检测器"""
    
    def __init__(self, config: Optional[YOLOConfig] = None):
        """
        初始化YOLO11检测器
        
        Args:
            config: YOLO配置
        """
        self.config = config or YOLOConfig()
        self.model = None
        self.device = self._get_device()
        self._load_model()
    
    def _get_device(self) -> str:
        """获取计算设备"""
        if self.config.device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                return "mps"  # Apple Silicon
            else:
                return "cpu"
        return self.config.device
    
    def _load_model(self):
        """加载YOLO11模型"""
        try:
            logger.info(f"正在加载YOLO11模型: {self.config.model_name}")
            
            # 加载预训练模型
            self.model = YOLO(self.config.model_name)
            
            # 设置设备
            if self.device != "cpu":
                self.model.to(self.device)
            
            logger.info(f"YOLO11模型加载成功，使用设备: {self.device}")
            
        except Exception as e:
            logger.error(f"加载YOLO11模型失败: {e}")
            raise
    
    def detect(self, image: np.ndarray, return_crops: bool = False) -> List[Dict[str, Any]]:
        """
        检测图像中的目标
        
        Args:
            image: 输入图像 (BGR格式)
            return_crops: 是否返回裁剪的区域图像
            
        Returns:
            检测结果列表，每个结果包含bbox, confidence, class等信息
        """
        if self.model is None:
            raise RuntimeError("YOLO模型未加载")
        
        try:
            # 执行检测
            results = self.model(
                image,
                conf=self.config.confidence,
                iou=self.config.iou_threshold,
                max_det=self.config.max_detections,
                imgsz=self.config.input_size,
                verbose=False
            )
            
            # 解析结果
            detections = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for i in range(len(boxes)):
                        detection = self._parse_detection(boxes, i, image, return_crops)
                        detections.append(detection)
            
            # 按置信度排序
            detections.sort(key=lambda x: x['confidence'], reverse=True)
            
            logger.debug(f"检测到 {len(detections)} 个目标")
            return detections
            
        except Exception as e:
            logger.error(f"YOLO检测失败: {e}")
            return []
    
    def _parse_detection(self, boxes, index: int, image: np.ndarray, 
                        return_crops: bool) -> Dict[str, Any]:
        """
        解析单个检测结果
        
        Args:
            boxes: YOLO检测框对象
            index: 检测框索引
            image: 原始图像
            return_crops: 是否返回裁剪图像
            
        Returns:
            解析后的检测结果
        """
        # 获取边界框坐标 (xyxy格式)
        xyxy = boxes.xyxy[index].cpu().numpy()
        x1, y1, x2, y2 = map(int, xyxy)
        
        # 获取置信度
        confidence = float(boxes.conf[index].cpu().numpy())
        
        # 获取类别
        class_id = int(boxes.cls[index].cpu().numpy())
        
        # 计算边界框面积
        bbox_area = (x2 - x1) * (y2 - y1)
        
        detection = {
            'bbox': [x1, y1, x2, y2],
            'confidence': confidence,
            'class_id': class_id,
            'bbox_area': bbox_area,
            'center': [(x1 + x2) // 2, (y1 + y2) // 2]
        }
        
        # 如果需要返回裁剪图像
        if return_crops:
            crop = self._crop_detection(image, [x1, y1, x2, y2])
            detection['crop'] = crop
        
        return detection
    
    def _crop_detection(self, image: np.ndarray, bbox: List[int]) -> np.ndarray:
        """
        裁剪检测区域
        
        Args:
            image: 原始图像
            bbox: 边界框 [x1, y1, x2, y2]
            
        Returns:
            裁剪后的图像区域
        """
        x1, y1, x2, y2 = bbox
        
        # 确保坐标在图像范围内
        h, w = image.shape[:2]
        x1 = max(0, min(x1, w))
        y1 = max(0, min(y1, h))
        x2 = max(0, min(x2, w))
        y2 = max(0, min(y2, h))
        
        return image[y1:y2, x1:x2]
    
    def detect_numbers(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        专门检测数字区域
        
        Args:
            image: 输入图像
            
        Returns:
            数字区域检测结果
        """
        # 执行通用检测
        detections = self.detect(image, return_crops=True)
        
        # 过滤和优化数字检测结果
        number_detections = []
        for detection in detections:
            # 这里可以添加特定的数字区域过滤逻辑
            # 例如：基于尺寸、位置、形状等特征
            if self._is_likely_number_region(detection):
                number_detections.append(detection)
        
        return number_detections
    
    def _is_likely_number_region(self, detection: Dict[str, Any]) -> bool:
        """
        判断检测区域是否可能是数字区域
        
        Args:
            detection: 检测结果
            
        Returns:
            是否可能是数字区域
        """
        # 基于面积过滤：太小或太大的区域可能不是数字
        bbox_area = detection['bbox_area']
        if bbox_area < 100 or bbox_area > 50000:
            return False
        
        # 基于宽高比过滤：数字通常有特定的宽高比
        x1, y1, x2, y2 = detection['bbox']
        width = x2 - x1
        height = y2 - y1
        
        if height == 0:
            return False
        
        aspect_ratio = width / height
        # 数字的宽高比通常在0.2到5.0之间
        if aspect_ratio < 0.2 or aspect_ratio > 5.0:
            return False
        
        return True
    
    def visualize_detections(self, image: np.ndarray, 
                           detections: List[Dict[str, Any]]) -> np.ndarray:
        """
        可视化检测结果
        
        Args:
            image: 原始图像
            detections: 检测结果列表
            
        Returns:
            标注了检测框的图像
        """
        vis_image = image.copy()
        
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            confidence = detection['confidence']
            
            # 绘制边界框
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 绘制置信度标签
            label = f"Conf: {confidence:.3f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
            cv2.rectangle(vis_image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), (0, 255, 0), -1)
            cv2.putText(vis_image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
        
        return vis_image
    
    def update_config(self, **kwargs):
        """
        更新配置参数
        
        Args:
            **kwargs: 要更新的配置参数
        """
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                logger.info(f"更新YOLO配置: {key} = {value}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        if self.model is None:
            return {}
        
        return {
            'model_name': self.config.model_name,
            'device': self.device,
            'input_size': self.config.input_size,
            'confidence_threshold': self.config.confidence,
            'iou_threshold': self.config.iou_threshold,
            'max_detections': self.config.max_detections
        }
