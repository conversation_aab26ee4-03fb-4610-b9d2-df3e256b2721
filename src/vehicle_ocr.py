"""
车辆自编码识别系统主模块
Main module for vehicle number OCR system
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import time
from loguru import logger

from .models.yolo_detector import YOLODetector
from .models.ocr_recognizer import OCRRecognizer
from .utils.config import Config
from .utils.image_processor import ImageProcessor
from .utils.validator import ResultValidator


class VehicleOCR:
    """车辆自编码识别系统"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        初始化车辆OCR系统
        
        Args:
            config: 系统配置
        """
        self.config = config or Config()
        
        # 初始化各个组件
        logger.info("初始化车辆OCR系统...")
        self.image_processor = ImageProcessor(self.config.image_process)
        self.detector = YOLODetector(self.config.yolo)
        self.ocr = OCRRecognizer(self.config.ocr)
        self.validator = ResultValidator(self.config.validation)
        
        logger.info("车辆OCR系统初始化完成")
    
    def recognize(self, image_input: Union[str, Path, np.ndarray], 
                 return_details: bool = False) -> Dict[str, Any]:
        """
        识别车辆自编码
        
        Args:
            image_input: 图像输入（路径或numpy数组）
            return_details: 是否返回详细信息
            
        Returns:
            识别结果字典
        """
        start_time = time.time()
        
        try:
            # 加载图像
            if isinstance(image_input, (str, Path)):
                image = self.image_processor.load_image(image_input)
                image_path = str(image_input)
            else:
                image = image_input.copy()
                image_path = "memory_image"
            
            logger.debug(f"开始处理图像: {image_path}")
            
            # 图像预处理
            processed_image = self.image_processor.preprocess(image, enhance=True)
            
            # 目标检测 - 定位数字区域
            detections = self.detector.detect_numbers(image)
            
            if not detections:
                return self._create_result(
                    success=False,
                    error="未检测到数字区域",
                    processing_time=time.time() - start_time,
                    image_path=image_path
                )
            
            # OCR识别
            all_ocr_results = []
            for detection in detections:
                # 获取检测区域
                bbox = detection['bbox']
                detection_confidence = detection['confidence']
                
                # 在检测区域进行OCR识别
                ocr_results = self.ocr.recognize_region(image, bbox)
                
                # 为每个OCR结果添加检测信息
                for ocr_result in ocr_results:
                    ocr_result['detection_confidence'] = detection_confidence
                    ocr_result['detection_bbox'] = bbox
                    
                    # 计算综合置信度
                    combined_confidence = self.validator.calculate_confidence_score(
                        ocr_result['confidence'],
                        detection_confidence
                    )
                    ocr_result['combined_confidence'] = combined_confidence
                
                all_ocr_results.extend(ocr_results)
            
            # 验证和过滤结果
            valid_results = self.validator.filter_results(all_ocr_results)
            
            if not valid_results:
                return self._create_result(
                    success=False,
                    error="未识别到有效的数字编码",
                    processing_time=time.time() - start_time,
                    image_path=image_path,
                    detections=detections if return_details else None,
                    ocr_results=all_ocr_results if return_details else None
                )
            
            # 获取最佳结果
            best_result = valid_results[0]
            
            # 创建返回结果
            result = self._create_result(
                success=True,
                number=best_result['validation'].number,
                confidence=best_result['combined_confidence'],
                text=best_result['text'],
                processing_time=time.time() - start_time,
                image_path=image_path
            )
            
            # 添加详细信息
            if return_details:
                result.update({
                    'detections': detections,
                    'all_ocr_results': all_ocr_results,
                    'valid_results': valid_results,
                    'best_result': best_result
                })
            
            logger.info(f"识别成功: {result['number']} (置信度: {result['confidence']:.3f})")
            return result
            
        except Exception as e:
            logger.error(f"识别过程出错: {e}")
            return self._create_result(
                success=False,
                error=str(e),
                processing_time=time.time() - start_time,
                image_path=image_path if 'image_path' in locals() else "unknown"
            )
    
    def batch_recognize(self, image_inputs: List[Union[str, Path, np.ndarray]], 
                       return_details: bool = False) -> List[Dict[str, Any]]:
        """
        批量识别车辆自编码
        
        Args:
            image_inputs: 图像输入列表
            return_details: 是否返回详细信息
            
        Returns:
            识别结果列表
        """
        results = []
        total_count = len(image_inputs)
        
        logger.info(f"开始批量处理 {total_count} 张图像")
        
        for i, image_input in enumerate(image_inputs):
            logger.debug(f"处理第 {i+1}/{total_count} 张图像")
            result = self.recognize(image_input, return_details)
            results.append(result)
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        logger.info(f"批量处理完成: {success_count}/{total_count} 成功")
        
        return results
    
    def _create_result(self, success: bool, **kwargs) -> Dict[str, Any]:
        """
        创建标准化的结果字典
        
        Args:
            success: 是否成功
            **kwargs: 其他结果字段
            
        Returns:
            结果字典
        """
        result = {
            'success': success,
            'timestamp': time.time(),
            'number': kwargs.get('number'),
            'confidence': kwargs.get('confidence', 0.0),
            'text': kwargs.get('text', ''),
            'error': kwargs.get('error'),
            'processing_time': kwargs.get('processing_time', 0.0),
            'image_path': kwargs.get('image_path', ''),
        }
        
        # 添加可选字段
        optional_fields = ['detections', 'ocr_results', 'all_ocr_results', 
                          'valid_results', 'best_result']
        for field in optional_fields:
            if field in kwargs:
                result[field] = kwargs[field]
        
        return result
    
    def visualize_result(self, image_input: Union[str, Path, np.ndarray], 
                        result: Optional[Dict[str, Any]] = None) -> np.ndarray:
        """
        可视化识别结果
        
        Args:
            image_input: 图像输入
            result: 识别结果（如果为None则重新识别）
            
        Returns:
            可视化图像
        """
        # 加载图像
        if isinstance(image_input, (str, Path)):
            image = self.image_processor.load_image(image_input)
        else:
            image = image_input.copy()
        
        # 如果没有提供结果，则重新识别
        if result is None:
            result = self.recognize(image, return_details=True)
        
        vis_image = image.copy()
        
        # 绘制检测框
        if result.get('detections'):
            vis_image = self.detector.visualize_detections(vis_image, result['detections'])
        
        # 绘制OCR结果
        if result.get('all_ocr_results'):
            vis_image = self.ocr.visualize_results(vis_image, result['all_ocr_results'])
        
        # 绘制最终结果
        if result['success']:
            # 在图像顶部显示识别结果
            text = f"Number: {result['number']} (Conf: {result['confidence']:.3f})"
            font_scale = 1.0
            thickness = 2
            color = (0, 255, 0)  # 绿色
            
            text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness)[0]
            text_x = (vis_image.shape[1] - text_size[0]) // 2
            text_y = 30
            
            # 绘制背景
            cv2.rectangle(vis_image, (text_x - 10, text_y - text_size[1] - 10),
                         (text_x + text_size[0] + 10, text_y + 10), (0, 0, 0), -1)
            
            # 绘制文本
            cv2.putText(vis_image, text, (text_x, text_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, font_scale, color, thickness)
        else:
            # 显示错误信息
            error_text = f"Error: {result.get('error', 'Unknown error')}"
            cv2.putText(vis_image, error_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
        return vis_image
    
    def save_result_image(self, image_input: Union[str, Path, np.ndarray], 
                         save_path: Union[str, Path], 
                         result: Optional[Dict[str, Any]] = None):
        """
        保存可视化结果图像
        
        Args:
            image_input: 输入图像
            save_path: 保存路径
            result: 识别结果
        """
        vis_image = self.visualize_result(image_input, result)
        self.image_processor.save_image(vis_image, save_path)
        logger.info(f"结果图像已保存到: {save_path}")
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息
        
        Returns:
            系统信息字典
        """
        return {
            'yolo_info': self.detector.get_model_info(),
            'ocr_info': self.ocr.get_model_info(),
            'config': self.config.to_dict()
        }
    
    def update_config(self, **kwargs):
        """
        更新系统配置
        
        Args:
            **kwargs: 配置参数
        """
        # 更新各组件配置
        if 'yolo' in kwargs:
            self.detector.update_config(**kwargs['yolo'])
        
        if 'ocr' in kwargs:
            self.ocr.update_config(**kwargs['ocr'])
        
        if 'validation' in kwargs:
            for key, value in kwargs['validation'].items():
                if hasattr(self.config.validation, key):
                    setattr(self.config.validation, key, value)
        
        logger.info("系统配置已更新")
