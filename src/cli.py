"""
命令行接口
Command line interface for vehicle number OCR system
"""

import argparse
import json
import sys
from pathlib import Path
from typing import List, Dict, Any
import cv2
from loguru import logger

from .vehicle_ocr import VehicleOCR
from .utils.config import Config


def setup_logging(verbose: bool = False):
    """设置日志"""
    logger.remove()  # 移除默认处理器
    
    if verbose:
        logger.add(sys.stderr, level="DEBUG", 
                  format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
    else:
        logger.add(sys.stderr, level="INFO",
                  format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>")


def recognize_single(args) -> int:
    """识别单张图像"""
    try:
        # 检查输入文件
        image_path = Path(args.image)
        if not image_path.exists():
            logger.error(f"图像文件不存在: {image_path}")
            return 1
        
        # 加载配置
        config = None
        if args.config:
            config = Config.from_yaml(args.config)
        
        # 初始化OCR系统
        ocr_system = VehicleOCR(config)
        
        # 执行识别
        result = ocr_system.recognize(image_path, return_details=args.details)
        
        # 输出结果
        if args.output:
            # 保存到文件
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            logger.info(f"结果已保存到: {args.output}")
        else:
            # 打印到控制台
            if result['success']:
                print(f"识别结果: {result['number']}")
                print(f"置信度: {result['confidence']:.3f}")
                print(f"原始文本: {result['text']}")
                print(f"处理时间: {result['processing_time']:.3f}秒")
            else:
                print(f"识别失败: {result['error']}")
        
        # 保存可视化结果
        if args.save_vis:
            vis_path = Path(args.save_vis)
            ocr_system.save_result_image(image_path, vis_path, result)
        
        return 0 if result['success'] else 1
        
    except Exception as e:
        logger.error(f"处理失败: {e}")
        return 1


def recognize_batch(args) -> int:
    """批量识别"""
    try:
        input_dir = Path(args.input_dir)
        if not input_dir.exists():
            logger.error(f"输入目录不存在: {input_dir}")
            return 1
        
        # 查找图像文件
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        image_files = []
        for ext in image_extensions:
            image_files.extend(input_dir.glob(f"*{ext}"))
            image_files.extend(input_dir.glob(f"*{ext.upper()}"))
        
        if not image_files:
            logger.error(f"在目录中未找到图像文件: {input_dir}")
            return 1
        
        logger.info(f"找到 {len(image_files)} 个图像文件")
        
        # 加载配置
        config = None
        if args.config:
            config = Config.from_yaml(args.config)
        
        # 初始化OCR系统
        ocr_system = VehicleOCR(config)
        
        # 批量处理
        results = ocr_system.batch_recognize(image_files, return_details=args.details)
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        
        # 保存结果
        output_data = {
            'summary': {
                'total_images': total_count,
                'successful_recognitions': success_count,
                'success_rate': success_count / total_count if total_count > 0 else 0
            },
            'results': results
        }
        
        output_path = Path(args.output)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"批量处理完成: {success_count}/{total_count} 成功")
        logger.info(f"结果已保存到: {output_path}")
        
        # 保存可视化结果
        if args.save_vis_dir:
            vis_dir = Path(args.save_vis_dir)
            vis_dir.mkdir(parents=True, exist_ok=True)
            
            for i, (image_file, result) in enumerate(zip(image_files, results)):
                vis_path = vis_dir / f"{image_file.stem}_result{image_file.suffix}"
                ocr_system.save_result_image(image_file, vis_path, result)
            
            logger.info(f"可视化结果已保存到: {vis_dir}")
        
        return 0
        
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        return 1


def show_info(args) -> int:
    """显示系统信息"""
    try:
        # 加载配置
        config = None
        if args.config:
            config = Config.from_yaml(args.config)
        
        # 初始化OCR系统
        ocr_system = VehicleOCR(config)
        
        # 获取系统信息
        info = ocr_system.get_system_info()
        
        print("=== 车辆OCR系统信息 ===")
        print(f"YOLO模型: {info['yolo_info']['model_name']}")
        print(f"使用设备: {info['yolo_info']['device']}")
        print(f"输入尺寸: {info['yolo_info']['input_size']}")
        print(f"置信度阈值: {info['yolo_info']['confidence_threshold']}")
        print(f"OCR语言: {info['ocr_info']['lang']}")
        print(f"OCR使用GPU: {info['ocr_info']['use_gpu']}")
        print(f"数字范围: {info['config']['validation']['number_range']}")
        
        return 0
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        return 1


def create_config(args) -> int:
    """创建配置文件"""
    try:
        config = Config()
        config.save_yaml(args.output)
        logger.info(f"默认配置文件已创建: {args.output}")
        return 0
        
    except Exception as e:
        logger.error(f"创建配置文件失败: {e}")
        return 1


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="车辆自编码识别系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 识别单张图像
  python -m src.cli recognize --image vehicle.jpg
  
  # 批量识别并保存结果
  python -m src.cli batch --input_dir images/ --output results.json
  
  # 显示系统信息
  python -m src.cli info
  
  # 创建默认配置文件
  python -m src.cli create-config --output config.yaml
        """
    )
    
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--config', '-c', help='配置文件路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 单张图像识别
    recognize_parser = subparsers.add_parser('recognize', help='识别单张图像')
    recognize_parser.add_argument('--image', '-i', required=True, help='输入图像路径')
    recognize_parser.add_argument('--output', '-o', help='输出结果文件路径 (JSON格式)')
    recognize_parser.add_argument('--save-vis', help='保存可视化结果图像路径')
    recognize_parser.add_argument('--details', action='store_true', help='返回详细信息')
    
    # 批量识别
    batch_parser = subparsers.add_parser('batch', help='批量识别')
    batch_parser.add_argument('--input-dir', '-i', required=True, help='输入图像目录')
    batch_parser.add_argument('--output', '-o', required=True, help='输出结果文件路径 (JSON格式)')
    batch_parser.add_argument('--save-vis-dir', help='保存可视化结果图像目录')
    batch_parser.add_argument('--details', action='store_true', help='返回详细信息')
    
    # 系统信息
    info_parser = subparsers.add_parser('info', help='显示系统信息')
    
    # 创建配置文件
    config_parser = subparsers.add_parser('create-config', help='创建默认配置文件')
    config_parser.add_argument('--output', '-o', required=True, help='配置文件输出路径')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    
    # 执行命令
    if args.command == 'recognize':
        return recognize_single(args)
    elif args.command == 'batch':
        return recognize_batch(args)
    elif args.command == 'info':
        return show_info(args)
    elif args.command == 'create-config':
        return create_config(args)
    else:
        parser.print_help()
        return 1


if __name__ == '__main__':
    sys.exit(main())
