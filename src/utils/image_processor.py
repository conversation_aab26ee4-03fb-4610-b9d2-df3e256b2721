"""
图像处理模块
Image processing utilities for vehicle number OCR system
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance
from typing import Union, Tuple, Optional, List
import albumentations as A
from pathlib import Path

from .config import ImageProcessConfig


class ImageProcessor:
    """图像处理器"""
    
    def __init__(self, config: Optional[ImageProcessConfig] = None):
        """
        初始化图像处理器
        
        Args:
            config: 图像处理配置
        """
        self.config = config or ImageProcessConfig()
        self._setup_transforms()
    
    def _setup_transforms(self):
        """设置图像变换"""
        # 基础预处理变换
        self.basic_transform = A.Compose([
            <PERSON>.<PERSON>size(
                height=self.config.target_size[0],
                width=self.config.target_size[1],
                interpolation=cv2.INTER_LINEAR
            ),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            if self.config.normalize else A.NoOp(),
        ])
        
        # 增强变换（用于训练数据增强）
        self.augment_transform = A.<PERSON>([
            A.RandomBrightnessContrast(
                brightness_limit=0.2,
                contrast_limit=0.2,
                p=0.5
            ),
            A.GaussNoise(var_limit=(10.0, 50.0), p=0.3),
            A.MotionBlur(blur_limit=3, p=0.3),
            A.RandomGamma(gamma_limit=(80, 120), p=0.3),
            A.HueSaturationValue(
                hue_shift_limit=10,
                sat_shift_limit=20,
                val_shift_limit=20,
                p=0.3
            ),
        ])
    
    def load_image(self, image_path: Union[str, Path]) -> np.ndarray:
        """
        加载图像
        
        Args:
            image_path: 图像路径
            
        Returns:
            numpy数组格式的图像 (BGR格式)
        """
        image_path = Path(image_path)
        if not image_path.exists():
            raise FileNotFoundError(f"图像文件不存在: {image_path}")
        
        # 使用OpenCV加载图像
        image = cv2.imread(str(image_path))
        if image is None:
            raise ValueError(f"无法加载图像: {image_path}")
        
        return image
    
    def preprocess(self, image: np.ndarray, enhance: bool = True) -> np.ndarray:
        """
        预处理图像
        
        Args:
            image: 输入图像 (BGR格式)
            enhance: 是否进行图像增强
            
        Returns:
            预处理后的图像
        """
        # 转换为RGB格式
        if len(image.shape) == 3 and image.shape[2] == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = image
        
        # 图像增强
        if enhance:
            image_rgb = self._enhance_image(image_rgb)
        
        # 去噪
        if self.config.denoise:
            image_rgb = self._denoise_image(image_rgb)
        
        # 应用基础变换
        transformed = self.basic_transform(image=image_rgb)
        processed_image = transformed["image"]
        
        return processed_image
    
    def _enhance_image(self, image: np.ndarray) -> np.ndarray:
        """
        增强图像质量
        
        Args:
            image: 输入图像 (RGB格式)
            
        Returns:
            增强后的图像
        """
        # 转换为PIL图像进行增强
        pil_image = Image.fromarray(image)
        
        # 对比度增强
        if self.config.enhance_contrast:
            enhancer = ImageEnhance.Contrast(pil_image)
            pil_image = enhancer.enhance(1.2)
        
        # 亮度调整
        enhancer = ImageEnhance.Brightness(pil_image)
        pil_image = enhancer.enhance(1.1)
        
        # 锐度增强
        enhancer = ImageEnhance.Sharpness(pil_image)
        pil_image = enhancer.enhance(1.1)
        
        return np.array(pil_image)
    
    def _denoise_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像去噪
        
        Args:
            image: 输入图像
            
        Returns:
            去噪后的图像
        """
        # 使用双边滤波去噪
        denoised = cv2.bilateralFilter(image, 9, 75, 75)
        return denoised
    
    def crop_region(self, image: np.ndarray, bbox: Tuple[int, int, int, int]) -> np.ndarray:
        """
        裁剪图像区域
        
        Args:
            image: 输入图像
            bbox: 边界框 (x1, y1, x2, y2)
            
        Returns:
            裁剪后的图像区域
        """
        x1, y1, x2, y2 = bbox
        
        # 确保坐标在图像范围内
        h, w = image.shape[:2]
        x1 = max(0, min(x1, w))
        y1 = max(0, min(y1, h))
        x2 = max(0, min(x2, w))
        y2 = max(0, min(y2, h))
        
        return image[y1:y2, x1:x2]
    
    def resize_image(self, image: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
        """
        调整图像大小
        
        Args:
            image: 输入图像
            target_size: 目标尺寸 (width, height)
            
        Returns:
            调整大小后的图像
        """
        return cv2.resize(image, target_size, interpolation=cv2.INTER_LINEAR)
    
    def augment_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像数据增强
        
        Args:
            image: 输入图像 (RGB格式)
            
        Returns:
            增强后的图像
        """
        augmented = self.augment_transform(image=image)
        return augmented["image"]
    
    def normalize_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像归一化
        
        Args:
            image: 输入图像
            
        Returns:
            归一化后的图像
        """
        if image.dtype != np.float32:
            image = image.astype(np.float32) / 255.0
        
        # ImageNet标准化
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])
        
        normalized = (image - mean) / std
        return normalized
    
    def save_image(self, image: np.ndarray, save_path: Union[str, Path]):
        """
        保存图像
        
        Args:
            image: 要保存的图像
            save_path: 保存路径
        """
        save_path = Path(save_path)
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 如果是归一化的图像，需要反归一化
        if image.dtype == np.float32 and image.max() <= 1.0:
            image = (image * 255).astype(np.uint8)
        
        # 如果是RGB格式，转换为BGR格式保存
        if len(image.shape) == 3 and image.shape[2] == 3:
            image_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
        else:
            image_bgr = image
        
        cv2.imwrite(str(save_path), image_bgr)
