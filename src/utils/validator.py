"""
结果验证模块
Result validation utilities for vehicle number OCR system
"""

import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import numpy as np

from .config import ValidationConfig


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    confidence: float
    number: Optional[int]
    raw_text: str
    error_message: Optional[str] = None


class ResultValidator:
    """结果验证器"""
    
    def __init__(self, config: Optional[ValidationConfig] = None):
        """
        初始化验证器
        
        Args:
            config: 验证配置
        """
        self.config = config or ValidationConfig()
        self._setup_patterns()
    
    def _setup_patterns(self):
        """设置正则表达式模式"""
        # 数字模式：匹配1-4位数字
        self.number_pattern = re.compile(r'\b\d{1,4}\b')
        
        # 严格数字模式：只包含数字
        self.strict_number_pattern = re.compile(r'^\d+$')
        
        # 清理模式：移除非数字字符
        self.clean_pattern = re.compile(r'[^\d]')
    
    def validate_ocr_result(self, ocr_result: Dict[str, Any]) -> ValidationResult:
        """
        验证OCR识别结果
        
        Args:
            ocr_result: OCR识别结果，包含text和confidence
            
        Returns:
            验证结果
        """
        raw_text = ocr_result.get('text', '').strip()
        confidence = ocr_result.get('confidence', 0.0)
        
        # 检查置信度
        if confidence < self.config.min_confidence:
            return ValidationResult(
                is_valid=False,
                confidence=confidence,
                number=None,
                raw_text=raw_text,
                error_message=f"置信度过低: {confidence:.3f} < {self.config.min_confidence}"
            )
        
        # 提取数字
        extracted_number = self._extract_number(raw_text)
        if extracted_number is None:
            return ValidationResult(
                is_valid=False,
                confidence=confidence,
                number=None,
                raw_text=raw_text,
                error_message=f"无法从文本中提取有效数字: '{raw_text}'"
            )
        
        # 验证数字范围
        if not self._is_in_valid_range(extracted_number):
            return ValidationResult(
                is_valid=False,
                confidence=confidence,
                number=extracted_number,
                raw_text=raw_text,
                error_message=f"数字超出有效范围 {self.config.number_range}: {extracted_number}"
            )
        
        return ValidationResult(
            is_valid=True,
            confidence=confidence,
            number=extracted_number,
            raw_text=raw_text
        )
    
    def _extract_number(self, text: str) -> Optional[int]:
        """
        从文本中提取数字
        
        Args:
            text: 输入文本
            
        Returns:
            提取的数字，如果无法提取则返回None
        """
        if not text:
            return None
        
        # 方法1: 直接匹配纯数字
        if self.strict_number_pattern.match(text):
            try:
                return int(text)
            except ValueError:
                pass
        
        # 方法2: 查找数字模式
        matches = self.number_pattern.findall(text)
        if matches:
            # 选择最长的匹配
            longest_match = max(matches, key=len)
            try:
                return int(longest_match)
            except ValueError:
                pass
        
        # 方法3: 清理非数字字符后尝试
        cleaned_text = self.clean_pattern.sub('', text)
        if cleaned_text:
            try:
                return int(cleaned_text)
            except ValueError:
                pass
        
        return None
    
    def _is_in_valid_range(self, number: int) -> bool:
        """
        检查数字是否在有效范围内
        
        Args:
            number: 要检查的数字
            
        Returns:
            是否在有效范围内
        """
        min_val, max_val = self.config.number_range
        return min_val <= number <= max_val
    
    def filter_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤和排序结果
        
        Args:
            results: OCR识别结果列表
            
        Returns:
            过滤后的结果列表
        """
        valid_results = []
        seen_numbers = set()
        
        # 按置信度排序
        sorted_results = sorted(results, key=lambda x: x.get('confidence', 0), reverse=True)
        
        for result in sorted_results:
            validation = self.validate_ocr_result(result)
            
            if validation.is_valid:
                # 去重
                if self.config.filter_duplicates and validation.number in seen_numbers:
                    continue
                
                seen_numbers.add(validation.number)
                
                # 添加验证信息到结果
                result['validation'] = validation
                valid_results.append(result)
                
                # 限制结果数量
                if len(valid_results) >= self.config.max_results:
                    break
        
        return valid_results
    
    def get_best_result(self, results: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        获取最佳结果
        
        Args:
            results: OCR识别结果列表
            
        Returns:
            最佳结果，如果没有有效结果则返回None
        """
        filtered_results = self.filter_results(results)
        
        if not filtered_results:
            return None
        
        # 返回置信度最高的结果
        return filtered_results[0]
    
    def calculate_confidence_score(self, 
                                 ocr_confidence: float,
                                 detection_confidence: float,
                                 text_quality_score: Optional[float] = None) -> float:
        """
        计算综合置信度分数
        
        Args:
            ocr_confidence: OCR识别置信度
            detection_confidence: 目标检测置信度
            text_quality_score: 文本质量分数（可选）
            
        Returns:
            综合置信度分数
        """
        # 基础权重
        weights = {
            'ocr': 0.6,
            'detection': 0.3,
            'text_quality': 0.1
        }
        
        # 计算加权平均
        total_score = (
            ocr_confidence * weights['ocr'] +
            detection_confidence * weights['detection']
        )
        
        if text_quality_score is not None:
            total_score += text_quality_score * weights['text_quality']
        else:
            # 重新分配权重
            total_score = (
                ocr_confidence * 0.7 +
                detection_confidence * 0.3
            )
        
        return min(1.0, max(0.0, total_score))
    
    def assess_text_quality(self, text: str, bbox_area: Optional[float] = None) -> float:
        """
        评估文本质量
        
        Args:
            text: 识别的文本
            bbox_area: 边界框面积（可选）
            
        Returns:
            文本质量分数 (0-1)
        """
        if not text:
            return 0.0
        
        score = 1.0
        
        # 长度惩罚：过短或过长的文本质量较低
        text_len = len(text.strip())
        if text_len < 1:
            score *= 0.1
        elif text_len > 4:
            score *= 0.8
        
        # 字符类型检查：纯数字质量最高
        if self.strict_number_pattern.match(text.strip()):
            score *= 1.0
        elif any(c.isdigit() for c in text):
            score *= 0.8
        else:
            score *= 0.3
        
        # 特殊字符惩罚
        special_chars = set('!@#$%^&*()_+-=[]{}|;:,.<>?')
        if any(c in special_chars for c in text):
            score *= 0.5
        
        return min(1.0, max(0.0, score))
