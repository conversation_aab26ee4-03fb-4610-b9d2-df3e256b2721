"""
配置管理模块
Configuration management for vehicle number OCR system
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, field


@dataclass
class YOLOConfig:
    """YOLO11配置"""
    model_name: str = "yolo11n.pt"  # 使用nano版本，速度快
    confidence: float = 0.25
    iou_threshold: float = 0.45
    max_detections: int = 10
    input_size: int = 640
    device: str = "auto"  # auto, cpu, cuda


@dataclass
class OCRConfig:
    """PaddleOCR配置"""
    use_angle_cls: bool = True
    lang: str = "ch"  # 支持中英文
    use_gpu: bool = True
    show_log: bool = False
    det_model_dir: Optional[str] = None
    rec_model_dir: Optional[str] = None
    cls_model_dir: Optional[str] = None


@dataclass
class ImageProcessConfig:
    """图像处理配置"""
    target_size: tuple = (640, 640)
    normalize: bool = True
    enhance_contrast: bool = True
    denoise: bool = True
    brightness_range: tuple = (0.8, 1.2)
    contrast_range: tuple = (0.8, 1.2)


@dataclass
class ValidationConfig:
    """验证配置"""
    number_range: tuple = (1, 1000)
    min_confidence: float = 0.5
    max_results: int = 5
    filter_duplicates: bool = True


@dataclass
class Config:
    """主配置类"""
    yolo: YOLOConfig = field(default_factory=YOLOConfig)
    ocr: OCRConfig = field(default_factory=OCRConfig)
    image_process: ImageProcessConfig = field(default_factory=ImageProcessConfig)
    validation: ValidationConfig = field(default_factory=ValidationConfig)
    
    # 路径配置
    project_root: Path = field(default_factory=lambda: Path(__file__).parent.parent.parent)
    data_dir: Path = field(init=False)
    models_dir: Path = field(init=False)
    logs_dir: Path = field(init=False)
    
    def __post_init__(self):
        """初始化后处理"""
        self.data_dir = self.project_root / "data"
        self.models_dir = self.data_dir / "models"
        self.logs_dir = self.project_root / "logs"
        
        # 创建必要的目录
        self.data_dir.mkdir(exist_ok=True)
        self.models_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
    
    @classmethod
    def from_yaml(cls, config_path: str) -> "Config":
        """从YAML文件加载配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)
        
        return cls.from_dict(config_dict)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "Config":
        """从字典创建配置"""
        config = cls()
        
        if "yolo" in config_dict:
            for key, value in config_dict["yolo"].items():
                if hasattr(config.yolo, key):
                    setattr(config.yolo, key, value)
        
        if "ocr" in config_dict:
            for key, value in config_dict["ocr"].items():
                if hasattr(config.ocr, key):
                    setattr(config.ocr, key, value)
        
        if "image_process" in config_dict:
            for key, value in config_dict["image_process"].items():
                if hasattr(config.image_process, key):
                    setattr(config.image_process, key, value)
        
        if "validation" in config_dict:
            for key, value in config_dict["validation"].items():
                if hasattr(config.validation, key):
                    setattr(config.validation, key, value)
        
        return config
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "yolo": {
                "model_name": self.yolo.model_name,
                "confidence": self.yolo.confidence,
                "iou_threshold": self.yolo.iou_threshold,
                "max_detections": self.yolo.max_detections,
                "input_size": self.yolo.input_size,
                "device": self.yolo.device,
            },
            "ocr": {
                "use_angle_cls": self.ocr.use_angle_cls,
                "lang": self.ocr.lang,
                "use_gpu": self.ocr.use_gpu,
                "show_log": self.ocr.show_log,
            },
            "image_process": {
                "target_size": self.image_process.target_size,
                "normalize": self.image_process.normalize,
                "enhance_contrast": self.image_process.enhance_contrast,
                "denoise": self.image_process.denoise,
                "brightness_range": self.image_process.brightness_range,
                "contrast_range": self.image_process.contrast_range,
            },
            "validation": {
                "number_range": self.validation.number_range,
                "min_confidence": self.validation.min_confidence,
                "max_results": self.validation.max_results,
                "filter_duplicates": self.validation.filter_duplicates,
            }
        }
    
    def save_yaml(self, config_path: str):
        """保存配置到YAML文件"""
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.to_dict(), f, default_flow_style=False, allow_unicode=True)


# 默认配置实例
default_config = Config()
