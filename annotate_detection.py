#!/usr/bin/env python3
"""
图像检测结果标注脚本
用于在图片上绘制检测框和文本标签
"""

import cv2
import numpy as np
from pathlib import Path
import json
from typing import List, Dict, Any, <PERSON><PERSON>


def draw_detection_box(image: np.ndarray, 
                      box_2d: List[int], 
                      text: str, 
                      box_color: Tuple[int, int, int] = (0, 0, 255),  # 红色
                      text_color: Tuple[int, int, int] = (255, 255, 255),  # 白色
                      text_bg_color: Tuple[int, int, int] = (0, 0, 255),  # 红色背景
                      thickness: int = 2,
                      font_scale: float = 0.8) -> np.ndarray:
    """
    在图像上绘制检测框和文本标签
    
    Args:
        image: 输入图像
        box_2d: 边界框坐标 [x1, y1, x2, y2]
        text: 要显示的文本
        box_color: 边界框颜色 (B, G, R)
        text_color: 文本颜色 (B, G, R)
        text_bg_color: 文本背景颜色 (B, G, R)
        thickness: 线条粗细
        font_scale: 字体大小
        
    Returns:
        标注后的图像
    """
    # 复制图像以避免修改原图
    result_image = image.copy()
    
    # 解析边界框坐标
    x1, y1, x2, y2 = box_2d
    
    # 绘制红色边界框
    cv2.rectangle(result_image, (x1, y1), (x2, y2), box_color, thickness)
    
    # 准备文本标签
    label = f'text: "{text}"'
    
    # 获取文本尺寸
    font = cv2.FONT_HERSHEY_SIMPLEX
    (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, thickness)
    
    # 计算文本位置（在框的上方）
    text_x = x1
    text_y = y1 - 10  # 在框上方10像素
    
    # 如果文本会超出图像顶部，则放在框内部顶部
    if text_y - text_height < 0:
        text_y = y1 + text_height + 10
    
    # 绘制文本背景矩形
    bg_x1 = text_x
    bg_y1 = text_y - text_height - 5
    bg_x2 = text_x + text_width + 10
    bg_y2 = text_y + 5
    
    cv2.rectangle(result_image, (bg_x1, bg_y1), (bg_x2, bg_y2), text_bg_color, -1)
    
    # 绘制文本
    cv2.putText(result_image, label, (text_x + 5, text_y), 
                font, font_scale, text_color, thickness)
    
    return result_image


def annotate_image_with_detections(image_path: str, 
                                 detections: List[Dict[str, Any]], 
                                 output_path: str = None) -> np.ndarray:
    """
    在图像上标注多个检测结果
    
    Args:
        image_path: 输入图像路径
        detections: 检测结果列表，每个元素包含 'box_2d' 和 'text' 字段
        output_path: 输出图像路径（可选）
        
    Returns:
        标注后的图像
    """
    # 加载图像
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法加载图像: {image_path}")
    
    print(f"原始图像尺寸: {image.shape}")
    
    # 对每个检测结果进行标注
    result_image = image.copy()
    for i, detection in enumerate(detections):
        box_2d = detection['box_2d']
        text = detection['text']
        
        print(f"标注检测结果 {i+1}: box_2d={box_2d}, text='{text}'")
        
        # 绘制检测框和文本
        result_image = draw_detection_box(result_image, box_2d, text)
    
    # 保存结果图像
    if output_path:
        cv2.imwrite(output_path, result_image)
        print(f"标注结果已保存到: {output_path}")
    
    return result_image


def main():
    """主函数"""
    # 输入图像路径
    image_path = "cq5_IP监控点3_cq5_20250711105923_1293100510.jpg"
    
    # 检测结果数据
    detections = [
  {"box_2d": [100, 137, 244, 208], "text": "268"}
]
    
    # 输出路径
    output_path = "annotated_" + Path(image_path).name
    
    try:
        # 检查输入文件是否存在
        if not Path(image_path).exists():
            print(f"错误: 图像文件不存在: {image_path}")
            return
        
        print(f"正在处理图像: {image_path}")
        print(f"检测结果数量: {len(detections)}")
        
        # 进行标注
        annotated_image = annotate_image_with_detections(
            image_path=image_path,
            detections=detections,
            output_path=output_path
        )
        
        print(f"标注完成！输出文件: {output_path}")
        
        # 显示图像信息
        print(f"输出图像尺寸: {annotated_image.shape}")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
