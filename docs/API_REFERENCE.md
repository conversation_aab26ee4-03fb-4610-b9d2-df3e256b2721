# API参考文档

## VehicleOCR 主类

### 初始化

```python
from src.vehicle_ocr import VehicleOCR
from src.utils.config import Config

# 使用默认配置
ocr = VehicleOCR()

# 使用自定义配置
config = Config()
config.yolo.confidence = 0.3
ocr = VehicleOCR(config)

# 从配置文件加载
config = Config.from_yaml('config.yaml')
ocr = VehicleOCR(config)
```

### 主要方法

#### recognize()
识别单张图像中的车辆自编码

```python
def recognize(image_input: Union[str, Path, np.ndarray], 
             return_details: bool = False) -> Dict[str, Any]
```

**参数:**
- `image_input`: 图像输入（文件路径或numpy数组）
- `return_details`: 是否返回详细信息

**返回值:**
```python
{
    'success': bool,           # 是否识别成功
    'number': int,             # 识别的数字（1-1000）
    'confidence': float,       # 综合置信度（0-1）
    'text': str,              # 原始识别文本
    'error': str,             # 错误信息（如果失败）
    'processing_time': float,  # 处理时间（秒）
    'timestamp': float,        # 时间戳
    'image_path': str         # 图像路径
}
```

**使用示例:**
```python
# 识别单张图像
result = ocr.recognize('vehicle.jpg')
if result['success']:
    print(f"识别结果: {result['number']}")
    print(f"置信度: {result['confidence']:.3f}")
else:
    print(f"识别失败: {result['error']}")

# 获取详细信息
detailed_result = ocr.recognize('vehicle.jpg', return_details=True)
print(f"检测到 {len(detailed_result['detections'])} 个目标区域")
```

#### batch_recognize()
批量识别多张图像

```python
def batch_recognize(image_inputs: List[Union[str, Path, np.ndarray]], 
                   return_details: bool = False) -> List[Dict[str, Any]]
```

**使用示例:**
```python
# 批量识别
image_files = ['img1.jpg', 'img2.jpg', 'img3.jpg']
results = ocr.batch_recognize(image_files)

for i, result in enumerate(results):
    if result['success']:
        print(f"图像 {i+1}: {result['number']}")
    else:
        print(f"图像 {i+1}: 识别失败")
```

#### visualize_result()
可视化识别结果

```python
def visualize_result(image_input: Union[str, Path, np.ndarray], 
                    result: Optional[Dict[str, Any]] = None) -> np.ndarray
```

**使用示例:**
```python
# 可视化结果
vis_image = ocr.visualize_result('vehicle.jpg')
cv2.imshow('Result', vis_image)
cv2.waitKey(0)

# 保存可视化结果
ocr.save_result_image('vehicle.jpg', 'result.jpg')
```

## 配置系统

### Config 类

```python
from src.utils.config import Config

# 创建默认配置
config = Config()

# 从YAML文件加载
config = Config.from_yaml('config.yaml')

# 从字典创建
config_dict = {
    'yolo': {'confidence': 0.3},
    'validation': {'min_confidence': 0.5}
}
config = Config.from_dict(config_dict)

# 保存配置
config.save_yaml('my_config.yaml')
```

### 配置参数

#### YOLO配置 (config.yolo)
- `model_name`: 模型文件名 (默认: "yolo11n.pt")
- `confidence`: 置信度阈值 (默认: 0.25)
- `iou_threshold`: IoU阈值 (默认: 0.45)
- `max_detections`: 最大检测数量 (默认: 10)
- `input_size`: 输入图像尺寸 (默认: 640)
- `device`: 计算设备 (默认: "auto")

#### OCR配置 (config.ocr)
- `use_angle_cls`: 是否使用角度分类器 (默认: True)
- `lang`: 语言设置 (默认: "ch")
- `use_gpu`: 是否使用GPU (默认: True)
- `show_log`: 是否显示日志 (默认: False)

#### 验证配置 (config.validation)
- `number_range`: 有效数字范围 (默认: (1, 1000))
- `min_confidence`: 最小置信度 (默认: 0.5)
- `max_results`: 最大结果数量 (默认: 5)
- `filter_duplicates`: 是否过滤重复结果 (默认: True)

## 工具类

### ImageProcessor
图像处理工具

```python
from src.utils.image_processor import ImageProcessor

processor = ImageProcessor()

# 加载图像
image = processor.load_image('image.jpg')

# 预处理
processed = processor.preprocess(image, enhance=True)

# 裁剪区域
bbox = [100, 100, 300, 200]  # [x1, y1, x2, y2]
cropped = processor.crop_region(image, bbox)

# 调整大小
resized = processor.resize_image(image, (640, 480))
```

### ResultValidator
结果验证工具

```python
from src.utils.validator import ResultValidator

validator = ResultValidator()

# 验证OCR结果
ocr_result = {'text': '123', 'confidence': 0.9}
validation = validator.validate_ocr_result(ocr_result)

if validation.is_valid:
    print(f"有效数字: {validation.number}")
else:
    print(f"验证失败: {validation.error_message}")

# 计算综合置信度
combined_conf = validator.calculate_confidence_score(
    ocr_confidence=0.8,
    detection_confidence=0.9
)
```

## 命令行接口

### 基本命令

```bash
# 识别单张图像
python -m src.cli recognize --image vehicle.jpg

# 批量识别
python -m src.cli batch --input-dir images/ --output results.json

# 显示系统信息
python -m src.cli info

# 创建配置文件
python -m src.cli create-config --output config.yaml
```

### 高级选项

```bash
# 使用自定义配置
python -m src.cli recognize --image vehicle.jpg --config my_config.yaml

# 保存可视化结果
python -m src.cli recognize --image vehicle.jpg --save-vis result.jpg

# 详细输出
python -m src.cli recognize --image vehicle.jpg --verbose --details

# 批量处理并保存可视化结果
python -m src.cli batch --input-dir images/ --output results.json --save-vis-dir vis_results/
```

## 错误处理

### 常见错误类型

1. **文件不存在**
```python
result = ocr.recognize('nonexistent.jpg')
# result['success'] = False
# result['error'] = "图像文件不存在: nonexistent.jpg"
```

2. **无法检测到数字区域**
```python
result = ocr.recognize('no_numbers.jpg')
# result['success'] = False
# result['error'] = "未检测到数字区域"
```

3. **识别结果超出范围**
```python
# 如果识别到的数字超出1-1000范围
result = ocr.recognize('large_number.jpg')
# result['success'] = False
# result['error'] = "未识别到有效的数字编码"
```

### 异常处理建议

```python
try:
    result = ocr.recognize('vehicle.jpg')
    if result['success']:
        print(f"识别成功: {result['number']}")
    else:
        print(f"识别失败: {result['error']}")
except Exception as e:
    print(f"系统错误: {e}")
```

## 性能优化

### 模型选择
- `yolo11n.pt`: 最快，精度较低
- `yolo11s.pt`: 平衡选择
- `yolo11m.pt`: 精度较高
- `yolo11l.pt`: 高精度
- `yolo11x.pt`: 最高精度，最慢

### 配置优化
```python
# 快速模式（牺牲精度）
config.yolo.model_name = "yolo11n.pt"
config.yolo.confidence = 0.1
config.yolo.input_size = 320

# 高精度模式（牺牲速度）
config.yolo.model_name = "yolo11x.pt"
config.yolo.confidence = 0.5
config.yolo.input_size = 1280
```

### GPU加速
```python
# 确保使用GPU
config.yolo.device = "cuda"
config.ocr.use_gpu = True
```
