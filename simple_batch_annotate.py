#!/usr/bin/env python3
"""
简化版批量生成带标注框的图片样本
"""

import cv2
import numpy as np
from pathlib import Path
import random
import re


def parse_annotation(annotation_path):
    """解析标注文件"""
    info = {'vehicle_number': None, 'position': None, 'bbox': None}
    
    with open(annotation_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    for line in lines:
        line = line.strip()
        if '车辆编号:' in line:
            match = re.search(r'车辆编号:\s*(\d+)', line)
            if match:
                info['vehicle_number'] = match.group(1)
        elif '位置:' in line:
            info['position'] = line.replace('# 位置:', '').strip()
        elif line.startswith('0 '):
            parts = line.split()
            if len(parts) == 5:
                _, cx, cy, w, h = map(float, parts)
                info['bbox'] = {'center_x': cx, 'center_y': cy, 'width': w, 'height': h}
    
    return info


def draw_annotation(image, info):
    """在图像上绘制标注"""
    if info['bbox'] is None:
        return image
    
    result = image.copy()
    h, w = image.shape[:2]
    
    # 转换坐标
    bbox = info['bbox']
    center_x = int(bbox['center_x'] * w)
    center_y = int(bbox['center_y'] * h)
    width = int(bbox['width'] * w)
    height = int(bbox['height'] * h)
    
    x1 = center_x - width // 2
    y1 = center_y - height // 2
    x2 = center_x + width // 2
    y2 = center_y + height // 2
    
    # 绘制边界框
    cv2.rectangle(result, (x1, y1), (x2, y2), (0, 255, 0), 2)
    
    # 绘制标签
    label = f"Number: {info.get('vehicle_number', 'Unknown')}"
    cv2.putText(result, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    # 绘制位置信息
    if info.get('position'):
        pos_text = f"Position: {info['position']}"
        cv2.putText(result, pos_text, (10, h-20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    return result


def main():
    """主函数"""
    print("开始生成带标注的样本图片...")
    
    # 创建输出目录
    output_dir = Path("simple_annotated_samples")
    output_dir.mkdir(exist_ok=True)
    
    # 找到匹配的文件对
    annotations_dir = Path("data/annotations")
    images_dir = Path("data/images")
    
    pairs = []
    for ann_file in annotations_dir.glob("*.txt"):
        if ann_file.name.startswith(('dataset_', 'train_', 'vehicle_')):
            continue
        
        img_name = ann_file.stem + ".jpg"
        img_file = images_dir / img_name
        
        if img_file.exists():
            pairs.append((ann_file, img_file))
    
    print(f"找到 {len(pairs)} 对匹配文件")
    
    # 随机选择8个样本
    random.seed(42)
    selected = random.sample(pairs, min(8, len(pairs)))
    
    print(f"处理 {len(selected)} 个样本:")
    
    for i, (ann_path, img_path) in enumerate(selected, 1):
        try:
            print(f"  [{i}] {img_path.name}")
            
            # 加载图像
            image = cv2.imread(str(img_path))
            if image is None:
                print(f"    无法加载图像")
                continue
            
            # 解析标注
            info = parse_annotation(str(ann_path))
            print(f"    车辆编号: {info.get('vehicle_number', 'Unknown')}")
            
            # 绘制标注
            annotated = draw_annotation(image, info)
            
            # 保存
            output_name = f"simple_{i:02d}_num{info.get('vehicle_number', 'unknown')}_{img_path.stem}.jpg"
            output_path = output_dir / output_name
            
            cv2.imwrite(str(output_path), annotated)
            print(f"    已保存: {output_name}")
            
        except Exception as e:
            print(f"    错误: {e}")
    
    print(f"\n完成！输出目录: {output_dir.absolute()}")


if __name__ == "__main__":
    main()
