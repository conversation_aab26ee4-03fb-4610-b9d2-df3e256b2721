#!/usr/bin/env python3
"""
车辆自编码识别系统快速启动脚本
Quick start script for vehicle number OCR system
"""

import sys
import cv2
import numpy as np
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.vehicle_ocr import VehicleOCR
from src.utils.config import Config


def create_demo_image():
    """创建演示图像"""
    # 创建一个模拟的车辆图像
    image = np.ones((480, 640, 3), dtype=np.uint8) * 200
    
    # 绘制车辆轮廓
    cv2.rectangle(image, (50, 150), (590, 400), (100, 100, 100), 3)
    
    # 绘制车门区域
    cv2.rectangle(image, (400, 200), (550, 350), (150, 150, 150), 2)
    
    # 在车门区域绘制数字 "168"
    cv2.putText(image, "168", (420, 290), cv2.FONT_HERSHEY_SIMPLEX, 
               2.5, (0, 0, 0), 6)
    
    # 添加一些噪声和变化
    noise = np.random.normal(0, 10, image.shape).astype(np.uint8)
    image = cv2.add(image, noise)
    
    return image


def main():
    """主函数"""
    logger.info("=== 车辆自编码识别系统快速启动 ===")
    
    try:
        # 1. 创建演示图像
        logger.info("1. 创建演示图像...")
        demo_image = create_demo_image()
        demo_path = project_root / "demo_vehicle.jpg"
        cv2.imwrite(str(demo_path), demo_image)
        logger.info(f"演示图像已保存: {demo_path}")
        
        # 2. 初始化OCR系统
        logger.info("2. 初始化OCR系统...")
        
        # 使用默认配置
        config = Config()
        
        # 为了快速演示，使用较低的配置要求
        config.yolo.model_name = "yolo11n.pt"  # 使用最小的模型
        config.yolo.confidence = 0.1  # 降低置信度阈值
        config.validation.min_confidence = 0.3  # 降低验证阈值
        
        ocr_system = VehicleOCR(config)
        logger.info("OCR系统初始化完成")
        
        # 3. 显示系统信息
        logger.info("3. 系统信息:")
        info = ocr_system.get_system_info()
        print(f"   YOLO模型: {info['yolo_info']['model_name']}")
        print(f"   使用设备: {info['yolo_info']['device']}")
        print(f"   OCR语言: {info['ocr_info']['lang']}")
        print(f"   数字范围: {info['config']['validation']['number_range']}")
        
        # 4. 执行识别
        logger.info("4. 执行识别...")
        result = ocr_system.recognize(demo_image, return_details=True)
        
        # 5. 显示结果
        logger.info("5. 识别结果:")
        if result['success']:
            print(f"   ✅ 识别成功!")
            print(f"   📊 识别数字: {result['number']}")
            print(f"   🎯 置信度: {result['confidence']:.3f}")
            print(f"   📝 原始文本: '{result['text']}'")
            print(f"   ⏱️  处理时间: {result['processing_time']:.3f}秒")
            
            # 显示详细信息
            if result.get('detections'):
                print(f"   🔍 检测到 {len(result['detections'])} 个目标区域")
            if result.get('all_ocr_results'):
                print(f"   📖 OCR识别到 {len(result['all_ocr_results'])} 个文本")
        else:
            print(f"   ❌ 识别失败: {result['error']}")
        
        # 6. 保存可视化结果
        logger.info("6. 保存可视化结果...")
        vis_path = project_root / "demo_result.jpg"
        ocr_system.save_result_image(demo_image, vis_path, result)
        logger.info(f"可视化结果已保存: {vis_path}")
        
        # 7. 命令行使用示例
        logger.info("7. 命令行使用示例:")
        print("   # 识别单张图像")
        print("   python -m src.cli recognize --image demo_vehicle.jpg")
        print()
        print("   # 显示系统信息")
        print("   python -m src.cli info")
        print()
        print("   # 创建配置文件")
        print("   python -m src.cli create-config --output my_config.yaml")
        
        # 8. Python API使用示例
        logger.info("8. Python API使用示例:")
        print("""
   from src.vehicle_ocr import VehicleOCR
   
   # 初始化系统
   ocr = VehicleOCR()
   
   # 识别图像
   result = ocr.recognize("path/to/image.jpg")
   
   if result['success']:
       print(f"识别结果: {result['number']}")
       print(f"置信度: {result['confidence']}")
   else:
       print(f"识别失败: {result['error']}")
        """)
        
        logger.info("=== 快速启动完成 ===")
        logger.info("💡 提示: 请将您的车辆图像放在项目目录中，然后使用上述命令进行识别")
        
        return 0
        
    except Exception as e:
        logger.error(f"快速启动失败: {e}")
        logger.error("请检查依赖是否正确安装:")
        logger.error("pip install -r requirements.txt")
        return 1


if __name__ == "__main__":
    sys.exit(main())
