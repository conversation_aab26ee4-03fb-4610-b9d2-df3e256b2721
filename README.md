# 车辆自编码识别系统 (Vehicle Number OCR System)

基于YOLO11和PaddleOCR的车辆自编码识别系统，用于识别车门旁边喷涂的1-1000范围内的数字编码。

## 项目概述

### 技术特点
- **目标检测**: 使用YOLO11进行车门数字区域定位
- **OCR识别**: 使用PaddleOCR进行数字识别
- **高精度**: YOLO11相比YOLOv8参数减少22%但精度更高
- **实时处理**: 支持实时图像处理和批量处理
- **范围验证**: 自动验证识别结果是否在1-1000范围内

### 系统架构
```
输入图像 → 图像预处理 → YOLO11目标检测 → OCR识别 → 结果验证 → 输出编码
    ↓           ↓            ↓           ↓         ↓
  OpenCV    图像增强      数字区域定位   PaddleOCR  范围验证
```

## 环境要求

- Python 3.12+
- PyTorch 2.0+
- CUDA 11.8+ (可选，用于GPU加速)

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 基本使用
```python
from src.vehicle_ocr import VehicleOCR

# 初始化识别器
ocr = VehicleOCR()

# 识别单张图像
result = ocr.recognize("path/to/vehicle_image.jpg")
print(f"识别结果: {result['number']}, 置信度: {result['confidence']}")

# 批量处理
results = ocr.batch_recognize(["image1.jpg", "image2.jpg"])
```

### 3. 命令行使用
```bash
# 识别单张图像
python -m src.cli recognize --image path/to/image.jpg

# 批量处理
python -m src.cli batch --input_dir images/ --output results.json
```

## 项目结构

```
truck_number_ocr/
├── src/                    # 源代码
│   ├── __init__.py
│   ├── vehicle_ocr.py      # 主要识别类
│   ├── models/             # 模型相关
│   │   ├── __init__.py
│   │   ├── yolo_detector.py    # YOLO11检测器
│   │   └── ocr_recognizer.py   # OCR识别器
│   ├── utils/              # 工具函数
│   │   ├── __init__.py
│   │   ├── image_processor.py  # 图像处理
│   │   ├── validator.py        # 结果验证
│   │   └── config.py          # 配置管理
│   └── cli.py              # 命令行接口
├── tests/                  # 测试文件
├── data/                   # 数据目录
│   ├── images/            # 测试图像
│   └── models/            # 预训练模型
├── docs/                   # 文档
├── requirements.txt        # 依赖列表
├── setup.py               # 安装脚本
└── README.md              # 项目说明
```

## 开发计划

- [x] 项目环境搭建
- [ ] 数据处理模块开发
- [ ] YOLO11目标检测模块
- [ ] OCR识别模块
- [ ] 主处理流程集成
- [ ] 测试和验证
- [ ] 文档和部署

## 技术细节

### YOLO11优势
- 相比YOLOv8参数减少22%，精度更高
- 增强的特征提取能力
- 优化的架构设计和训练流程
- 支持多种部署环境

### 处理流程
1. **图像预处理**: 调整尺寸、增强对比度、去噪
2. **目标检测**: 使用YOLO11定位数字区域
3. **OCR识别**: 使用PaddleOCR识别数字内容
4. **后处理**: 验证范围、置信度评估、结果过滤

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题请提交Issue或联系开发团队。
