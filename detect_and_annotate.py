#!/usr/bin/env python3
"""
使用通用OCR检测车辆编号并可视化结果
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import easyocr
import re
import os

def detect_vehicle_numbers(image_path, output_path):
    """
    检测图像中的车辆编号并用红框标注
    """
    # 初始化EasyOCR读取器
    reader = easyocr.Reader(['en', 'ch_sim'])
    
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return
    
    # 转换为RGB格式
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 使用EasyOCR检测文字
    results = reader.readtext(image_rgb)
    
    # 创建PIL图像用于绘制
    pil_image = Image.fromarray(image_rgb)
    draw = ImageDraw.Draw(pil_image)
    
    # 尝试使用系统字体，如果没有则使用默认字体
    try:
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 36)
    except:
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", 36)
        except:
            font = ImageFont.load_default()
    
    vehicle_numbers = []
    
    print("检测到的文字:")
    for (bbox, text, confidence) in results:
        print(f"文字: '{text}', 置信度: {confidence:.2f}")
        
        # 检查是否为数字（1-1000范围内的车辆编号）
        # 清理文字，去除空格和特殊字符
        cleaned_text = re.sub(r'[^\d]', '', text)
        
        if cleaned_text.isdigit():
            number = int(cleaned_text)
            if 1 <= number <= 1000 and confidence > 0.5:
                vehicle_numbers.append((bbox, text, number, confidence))
                
                # 绘制边界框
                points = np.array(bbox, dtype=np.int32)
                # 转换为PIL格式的坐标
                bbox_coords = [
                    (points[0][0], points[0][1]),
                    (points[1][0], points[1][1]),
                    (points[2][0], points[2][1]),
                    (points[3][0], points[3][1])
                ]
                
                # 绘制红色边界框
                draw.polygon(bbox_coords, outline='red', width=3)
                
                # 在框上方显示识别的数字
                text_position = (points[0][0], points[0][1] - 40)
                draw.text(text_position, f"Number: {number}", font=font, fill='red')
                
                print(f"识别车辆编号: {number} (置信度: {confidence:.2f})")
    
    # 保存结果图像
    pil_image.save(output_path)
    print(f"标注结果已保存到: {output_path}")
    
    if vehicle_numbers:
        print(f"\n共识别到 {len(vehicle_numbers)} 个车辆编号:")
        for bbox, original_text, number, conf in vehicle_numbers:
            print(f"  - 编号: {number} (原始文字: '{original_text}', 置信度: {conf:.2f})")
    else:
        print("未识别到有效的车辆编号")
    
    return vehicle_numbers

if __name__ == "__main__":
    # 输入图像路径
    input_image = "/Users/<USER>/zjssnf/truck_and_concrete_detection/truck_number_ocr/data/images/cq1_IP监控点2_cq1_20250711095148_27821307.jpg"
    
    # 输出图像路径
    output_image = "/Users/<USER>/zjssnf/truck_and_concrete_detection/truck_number_ocr/annotated_result.jpg"
    
    # 检测并标注车辆编号
    detect_vehicle_numbers(input_image, output_image)