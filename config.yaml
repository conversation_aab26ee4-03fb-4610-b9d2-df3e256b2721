# 车辆自编码识别系统配置文件
# Vehicle Number OCR System Configuration

# YOLO11检测配置
yolo:
  model_name: "yolo11n.pt"  # 模型文件名，可选: yolo11n.pt, yolo11s.pt, yolo11m.pt, yolo11l.pt, yolo11x.pt
  confidence: 0.25          # 置信度阈值
  iou_threshold: 0.45       # IoU阈值
  max_detections: 10        # 最大检测数量
  input_size: 640           # 输入图像尺寸
  device: "auto"            # 计算设备: auto, cpu, cuda

# PaddleOCR配置
ocr:
  use_angle_cls: true       # 是否使用角度分类器
  lang: "ch"                # 语言: ch(中英文), en(英文)
  use_gpu: true             # 是否使用GPU
  show_log: false           # 是否显示日志

# 图像处理配置
image_process:
  target_size: [640, 640]   # 目标尺寸 [width, height]
  normalize: true           # 是否归一化
  enhance_contrast: true    # 是否增强对比度
  denoise: true             # 是否去噪
  brightness_range: [0.8, 1.2]  # 亮度调整范围
  contrast_range: [0.8, 1.2]    # 对比度调整范围

# 验证配置
validation:
  number_range: [1, 1000]   # 有效数字范围
  min_confidence: 0.5       # 最小置信度
  max_results: 5            # 最大结果数量
  filter_duplicates: true   # 是否过滤重复结果
