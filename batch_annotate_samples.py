#!/usr/bin/env python3
"""
批量生成带标注框的图片样本
支持生成更多样本，并提供详细的统计信息
"""

import cv2
import numpy as np
from pathlib import Path
import random
import re
import json
from typing import List, Dict, Tuple, Optional
try:
    from collections import defaultdict
except ImportError:
    from collections import defaultdict


def parse_yolo_annotation(annotation_path: str) -> Dict:
    """解析YOLO格式的标注文件"""
    annotation_info = {
        'vehicle_number': None,
        'type': None,
        'position': None,
        'bbox': None,
        'filename': Path(annotation_path).name
    }
    
    with open(annotation_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 解析注释信息
    for line in lines:
        line = line.strip()
        if line.startswith('# 车辆编号:'):
            match = re.search(r'车辆编号:\s*(\d+)', line)
            if match:
                annotation_info['vehicle_number'] = match.group(1)
            # 提取类型信息
            if '类型:' in line:
                type_match = re.search(r'类型:\s*([^,]+)', line)
                if type_match:
                    annotation_info['type'] = type_match.group(1).strip()
        elif line.startswith('# 位置:'):
            annotation_info['position'] = line.replace('# 位置:', '').strip()
        elif line.startswith('0 '):
            parts = line.split()
            if len(parts) == 5:
                class_id, center_x, center_y, width, height = map(float, parts)
                annotation_info['bbox'] = {
                    'center_x': center_x,
                    'center_y': center_y,
                    'width': width,
                    'height': height
                }
    
    return annotation_info


def yolo_to_xyxy(bbox: Dict, img_width: int, img_height: int) -> Tuple[int, int, int, int]:
    """将YOLO格式转换为xyxy格式"""
    center_x = bbox['center_x'] * img_width
    center_y = bbox['center_y'] * img_height
    width = bbox['width'] * img_width
    height = bbox['height'] * img_height
    
    x1 = int(center_x - width / 2)
    y1 = int(center_y - height / 2)
    x2 = int(center_x + width / 2)
    y2 = int(center_y + height / 2)
    
    return x1, y1, x2, y2


def draw_enhanced_annotation(image: np.ndarray, annotation_info: Dict) -> np.ndarray:
    """绘制增强的标注信息"""
    result_image = image.copy()
    img_height, img_width = image.shape[:2]
    
    if annotation_info['bbox'] is None:
        return result_image
    
    # 转换边界框坐标
    x1, y1, x2, y2 = yolo_to_xyxy(annotation_info['bbox'], img_width, img_height)
    
    # 根据车辆编号选择颜色
    vehicle_number = annotation_info.get('vehicle_number', '0')
    color_map = {
        '1': (255, 0, 0),    # 蓝色
        '2': (0, 255, 0),    # 绿色
        '3': (0, 0, 255),    # 红色
        '4': (255, 255, 0),  # 青色
        '5': (255, 0, 255),  # 品红
        '6': (0, 255, 255),  # 黄色
    }
    
    # 根据编号最后一位选择颜色
    last_digit = vehicle_number[-1] if vehicle_number else '0'
    box_color = color_map.get(last_digit, (0, 255, 0))  # 默认绿色
    
    # 绘制边界框
    thickness = max(2, min(img_width, img_height) // 200)  # 根据图像大小调整线条粗细
    cv2.rectangle(result_image, (x1, y1), (x2, y2), box_color, thickness)
    
    # 绘制中心点
    center_x = (x1 + x2) // 2
    center_y = (y1 + y2) // 2
    cv2.circle(result_image, (center_x, center_y), 3, box_color, -1)
    
    # 准备标签文本
    vehicle_number = annotation_info.get('vehicle_number', 'Unknown')
    main_label = f"Number: {vehicle_number}"
    
    # 字体设置
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = max(0.5, min(img_width, img_height) / 1000)  # 根据图像大小调整字体
    text_thickness = max(1, thickness // 2)
    
    # 主标签
    (text_width, text_height), baseline = cv2.getTextSize(main_label, font, font_scale, text_thickness)
    
    # 计算文本位置
    text_x = x1
    text_y = y1 - 10
    
    if text_y - text_height < 0:
        text_y = y1 + text_height + 10
    
    # 绘制主标签背景和文本
    bg_x1, bg_y1 = text_x, text_y - text_height - 5
    bg_x2, bg_y2 = text_x + text_width + 10, text_y + 5
    cv2.rectangle(result_image, (bg_x1, bg_y1), (bg_x2, bg_y2), box_color, -1)
    cv2.putText(result_image, main_label, (text_x + 5, text_y), 
                font, font_scale, (255, 255, 255), text_thickness)
    
    # 添加类型信息（如果有）
    if annotation_info.get('type'):
        type_label = f"Type: {annotation_info['type']}"
        type_y = text_y + text_height + 15
        cv2.putText(result_image, type_label, (text_x, type_y), 
                    font, font_scale * 0.7, box_color, text_thickness)
    
    # 在图像底部添加位置信息
    if annotation_info.get('position'):
        position_text = f"Position: {annotation_info['position']}"
        pos_y = img_height - 10
        cv2.putText(result_image, position_text, (10, pos_y), 
                    font, font_scale * 0.6, (255, 255, 255), text_thickness)
    
    return result_image


def find_all_matching_pairs(annotations_dir: str, images_dir: str) -> List[Tuple[str, str]]:
    """找到所有匹配的标注-图像对"""
    annotations_path = Path(annotations_dir)
    images_path = Path(images_dir)
    
    pairs = []
    
    for annotation_file in annotations_path.glob("*.txt"):
        if annotation_file.name.startswith(('dataset_', 'train_', 'vehicle_')):
            continue
            
        image_name = annotation_file.stem + ".jpg"
        image_file = images_path / image_name
        
        if image_file.exists():
            pairs.append((str(annotation_file), str(image_file)))
    
    return pairs


def generate_batch_samples(num_samples: int = 10, output_dir: str = "batch_annotated_samples"):
    """批量生成带标注的样本"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 找到所有匹配对
    pairs = find_all_matching_pairs("data/annotations", "data/images")
    
    if not pairs:
        print("未找到匹配的标注-图像对")
        return
    
    print(f"找到 {len(pairs)} 对匹配的文件")
    
    # 随机选择样本
    selected_pairs = random.sample(pairs, min(num_samples, len(pairs)))
    
    # 统计信息
    stats = {
        'total_processed': 0,
        'successful': 0,
        'failed': 0,
        'vehicle_numbers': defaultdict(int),
        'image_sizes': [],
        'positions': defaultdict(int)
    }
    
    print(f"开始处理 {len(selected_pairs)} 个样本...")
    
    for i, (annotation_path, image_path) in enumerate(selected_pairs, 1):
        try:
            stats['total_processed'] += 1
            
            print(f"\n[{i:2d}/{len(selected_pairs)}] 处理: {Path(image_path).name}")
            
            # 加载图像
            image = cv2.imread(image_path)
            if image is None:
                print(f"  ❌ 无法加载图像")
                stats['failed'] += 1
                continue
            
            # 记录图像尺寸
            h, w = image.shape[:2]
            stats['image_sizes'].append((w, h))
            
            # 解析标注
            annotation_info = parse_yolo_annotation(annotation_path)
            vehicle_number = annotation_info.get('vehicle_number', 'Unknown')
            position = annotation_info.get('position', 'Unknown')
            
            print(f"  📋 车辆编号: {vehicle_number}")
            print(f"  📍 位置: {position}")
            print(f"  📐 图像尺寸: {w}x{h}")
            
            # 更新统计
            stats['vehicle_numbers'][vehicle_number] += 1
            stats['positions'][position] += 1
            
            # 绘制标注
            annotated_image = draw_enhanced_annotation(image, annotation_info)
            
            # 保存结果
            output_filename = f"batch_{i:02d}_num{vehicle_number}_{Path(image_path).stem}.jpg"
            output_filepath = output_path / output_filename
            
            cv2.imwrite(str(output_filepath), annotated_image)
            print(f"  ✅ 已保存: {output_filename}")
            
            stats['successful'] += 1
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            stats['failed'] += 1
    
    # 保存统计信息
    stats_file = output_path / "generation_stats.json"
    with open(stats_file, 'w', encoding='utf-8') as f:
        # 转换defaultdict为普通dict以便JSON序列化
        stats_dict = dict(stats)
        stats_dict['vehicle_numbers'] = dict(stats['vehicle_numbers'])
        stats_dict['positions'] = dict(stats['positions'])
        json.dump(stats_dict, f, ensure_ascii=False, indent=2)
    
    # 打印总结
    print(f"\n{'='*50}")
    print(f"批量处理完成！")
    print(f"总处理数: {stats['total_processed']}")
    print(f"成功数: {stats['successful']}")
    print(f"失败数: {stats['failed']}")
    print(f"输出目录: {output_path.absolute()}")
    print(f"统计文件: {stats_file}")
    
    # 打印车辆编号分布
    print(f"\n车辆编号分布:")
    for number, count in sorted(stats['vehicle_numbers'].items()):
        print(f"  {number}: {count} 张")


def main():
    """主函数"""
    print("=== 批量生成带标注框的图片样本 ===")
    
    # 设置随机种子
    random.seed(42)
    
    # 生成10个样本
    generate_batch_samples(num_samples=10, output_dir="batch_annotated_samples")


if __name__ == "__main__":
    main()
