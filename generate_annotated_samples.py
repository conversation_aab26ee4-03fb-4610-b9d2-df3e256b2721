#!/usr/bin/env python3
"""
随机生成带标注框的图片样本
从annotations和images目录中随机选择几组，生成可视化结果
"""

import cv2
import numpy as np
from pathlib import Path
import random
import re
from typing import List, Dict, Tuple, Optional


def parse_yolo_annotation(annotation_path: str) -> Dict:
    """
    解析YOLO格式的标注文件
    
    Args:
        annotation_path: 标注文件路径
        
    Returns:
        包含标注信息的字典
    """
    annotation_info = {
        'vehicle_number': None,
        'type': None,
        'position': None,
        'bbox': None
    }
    
    with open(annotation_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 解析注释信息
    for line in lines:
        line = line.strip()
        if line.startswith('# 车辆编号:'):
            # 提取车辆编号
            match = re.search(r'车辆编号:\s*(\d+)', line)
            if match:
                annotation_info['vehicle_number'] = match.group(1)
        elif line.startswith('# 位置:'):
            # 提取位置信息
            annotation_info['position'] = line.replace('# 位置:', '').strip()
        elif line.startswith('0 '):
            # 解析YOLO格式的边界框
            parts = line.split()
            if len(parts) == 5:
                class_id, center_x, center_y, width, height = map(float, parts)
                annotation_info['bbox'] = {
                    'center_x': center_x,
                    'center_y': center_y,
                    'width': width,
                    'height': height
                }
    
    return annotation_info


def yolo_to_xyxy(bbox: Dict, img_width: int, img_height: int) -> Tuple[int, int, int, int]:
    """
    将YOLO格式的边界框转换为xyxy格式
    
    Args:
        bbox: YOLO格式边界框 {center_x, center_y, width, height}
        img_width: 图像宽度
        img_height: 图像高度
        
    Returns:
        (x1, y1, x2, y2) 格式的边界框
    """
    center_x = bbox['center_x'] * img_width
    center_y = bbox['center_y'] * img_height
    width = bbox['width'] * img_width
    height = bbox['height'] * img_height
    
    x1 = int(center_x - width / 2)
    y1 = int(center_y - height / 2)
    x2 = int(center_x + width / 2)
    y2 = int(center_y + height / 2)
    
    return x1, y1, x2, y2


def draw_annotation_on_image(image: np.ndarray, 
                           annotation_info: Dict,
                           box_color: Tuple[int, int, int] = (0, 255, 0),  # 绿色
                           text_color: Tuple[int, int, int] = (255, 255, 255),  # 白色
                           text_bg_color: Tuple[int, int, int] = (0, 255, 0),  # 绿色背景
                           thickness: int = 2) -> np.ndarray:
    """
    在图像上绘制标注信息
    
    Args:
        image: 输入图像
        annotation_info: 标注信息
        box_color: 边界框颜色
        text_color: 文本颜色
        text_bg_color: 文本背景颜色
        thickness: 线条粗细
        
    Returns:
        标注后的图像
    """
    result_image = image.copy()
    img_height, img_width = image.shape[:2]
    
    if annotation_info['bbox'] is None:
        return result_image
    
    # 转换边界框坐标
    x1, y1, x2, y2 = yolo_to_xyxy(annotation_info['bbox'], img_width, img_height)
    
    # 绘制边界框
    cv2.rectangle(result_image, (x1, y1), (x2, y2), box_color, thickness)
    
    # 准备标签文本
    vehicle_number = annotation_info.get('vehicle_number', 'Unknown')
    label = f"Number: {vehicle_number}"
    
    # 获取文本尺寸
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.7
    (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, thickness)
    
    # 计算文本位置（在框的上方）
    text_x = x1
    text_y = y1 - 10
    
    # 如果文本会超出图像顶部，则放在框内部顶部
    if text_y - text_height < 0:
        text_y = y1 + text_height + 10
    
    # 绘制文本背景
    bg_x1 = text_x
    bg_y1 = text_y - text_height - 5
    bg_x2 = text_x + text_width + 10
    bg_y2 = text_y + 5
    
    cv2.rectangle(result_image, (bg_x1, bg_y1), (bg_x2, bg_y2), text_bg_color, -1)
    
    # 绘制文本
    cv2.putText(result_image, label, (text_x + 5, text_y), 
                font, font_scale, text_color, thickness)
    
    # 在图像底部添加位置信息
    if annotation_info.get('position'):
        position_text = f"Position: {annotation_info['position']}"
        pos_y = img_height - 20
        cv2.putText(result_image, position_text, (10, pos_y), 
                    font, 0.5, (255, 255, 255), 1)
    
    return result_image


def find_matching_pairs(annotations_dir: str, images_dir: str) -> List[Tuple[str, str]]:
    """
    找到匹配的标注文件和图像文件对
    
    Args:
        annotations_dir: 标注文件目录
        images_dir: 图像文件目录
        
    Returns:
        匹配的(标注文件路径, 图像文件路径)对列表
    """
    annotations_path = Path(annotations_dir)
    images_path = Path(images_dir)
    
    pairs = []
    
    # 遍历标注文件
    for annotation_file in annotations_path.glob("*.txt"):
        if annotation_file.name.startswith('dataset_') or annotation_file.name.startswith('train_') or annotation_file.name.startswith('vehicle_'):
            continue
            
        # 构造对应的图像文件名
        image_name = annotation_file.stem + ".jpg"
        image_file = images_path / image_name
        
        if image_file.exists():
            pairs.append((str(annotation_file), str(image_file)))
    
    return pairs


def generate_annotated_samples(num_samples: int = 5, output_dir: str = "annotated_samples"):
    """
    生成带标注的样本图片
    
    Args:
        num_samples: 要生成的样本数量
        output_dir: 输出目录
    """
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 找到所有匹配的标注-图像对
    pairs = find_matching_pairs("data/annotations", "data/images")
    
    if not pairs:
        print("未找到匹配的标注-图像对")
        return
    
    print(f"找到 {len(pairs)} 对匹配的文件")
    
    # 随机选择样本
    selected_pairs = random.sample(pairs, min(num_samples, len(pairs)))
    
    print(f"随机选择 {len(selected_pairs)} 个样本进行处理")
    
    for i, (annotation_path, image_path) in enumerate(selected_pairs, 1):
        try:
            print(f"\n处理样本 {i}/{len(selected_pairs)}")
            print(f"图像: {Path(image_path).name}")
            print(f"标注: {Path(annotation_path).name}")
            
            # 加载图像
            image = cv2.imread(image_path)
            if image is None:
                print(f"无法加载图像: {image_path}")
                continue
            
            print(f"图像尺寸: {image.shape}")
            
            # 解析标注
            annotation_info = parse_yolo_annotation(annotation_path)
            print(f"车辆编号: {annotation_info.get('vehicle_number', 'Unknown')}")
            print(f"位置: {annotation_info.get('position', 'Unknown')}")
            
            # 绘制标注
            annotated_image = draw_annotation_on_image(image, annotation_info)
            
            # 保存结果
            output_filename = f"sample_{i:02d}_{Path(image_path).stem}.jpg"
            output_filepath = output_path / output_filename
            
            cv2.imwrite(str(output_filepath), annotated_image)
            print(f"已保存: {output_filepath}")
            
        except Exception as e:
            print(f"处理样本 {i} 时出错: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n完成！共生成 {len(selected_pairs)} 个带标注的样本图片")
    print(f"输出目录: {output_path.absolute()}")


def main():
    """主函数"""
    print("=== 随机生成带标注框的图片样本 ===")
    
    # 设置随机种子以便复现
    random.seed(42)
    
    # 生成5个随机样本
    generate_annotated_samples(num_samples=5, output_dir="annotated_samples")


if __name__ == "__main__":
    main()
