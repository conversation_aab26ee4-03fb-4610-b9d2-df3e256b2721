#!/bin/bash

# 车辆自编码识别系统安装脚本
# Vehicle Number OCR System Installation Script

set -e  # 遇到错误时退出

echo "=== 车辆自编码识别系统安装脚本 ==="
echo "Vehicle Number OCR System Installation"
echo ""

# 检查Python版本
echo "1. 检查Python版本..."
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.12"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python版本过低: $python_version (需要 >= $required_version)"
    echo "请升级Python版本"
    exit 1
else
    echo "✅ Python版本检查通过: $python_version"
fi

# 检查pip
echo ""
echo "2. 检查pip..."
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3未找到，请安装pip"
    exit 1
else
    echo "✅ pip3检查通过"
fi

# 创建虚拟环境（可选）
echo ""
echo "3. 创建虚拟环境（可选）..."
read -p "是否创建虚拟环境？(y/n): " create_venv

if [ "$create_venv" = "y" ] || [ "$create_venv" = "Y" ]; then
    if [ ! -d "venv" ]; then
        echo "创建虚拟环境..."
        python3 -m venv venv
        echo "✅ 虚拟环境创建完成"
    else
        echo "✅ 虚拟环境已存在"
    fi
    
    echo "激活虚拟环境..."
    source venv/bin/activate
    echo "✅ 虚拟环境已激活"
    
    echo ""
    echo "💡 提示: 下次使用时请先激活虚拟环境:"
    echo "source venv/bin/activate"
fi

# 升级pip
echo ""
echo "4. 升级pip..."
pip3 install --upgrade pip
echo "✅ pip升级完成"

# 安装PyTorch (根据系统自动选择)
echo ""
echo "5. 安装PyTorch..."
if command -v nvidia-smi &> /dev/null; then
    echo "检测到NVIDIA GPU，安装CUDA版本的PyTorch..."
    pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
else
    echo "未检测到NVIDIA GPU，安装CPU版本的PyTorch..."
    pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
fi
echo "✅ PyTorch安装完成"

# 安装其他依赖
echo ""
echo "6. 安装项目依赖..."
pip3 install -r requirements.txt
echo "✅ 项目依赖安装完成"

# 安装项目
echo ""
echo "7. 安装项目..."
pip3 install -e .
echo "✅ 项目安装完成"

# 下载YOLO模型（可选）
echo ""
echo "8. 下载YOLO11模型（可选）..."
read -p "是否预下载YOLO11模型？(y/n): " download_model

if [ "$download_model" = "y" ] || [ "$download_model" = "Y" ]; then
    echo "下载YOLO11n模型..."
    python3 -c "
from ultralytics import YOLO
print('正在下载YOLO11n模型...')
model = YOLO('yolo11n.pt')
print('✅ YOLO11n模型下载完成')
"
fi

# 运行测试
echo ""
echo "9. 运行测试..."
read -p "是否运行测试？(y/n): " run_tests

if [ "$run_tests" = "y" ] || [ "$run_tests" = "Y" ]; then
    echo "运行单元测试..."
    python3 -m pytest tests/ -v
    echo "✅ 测试完成"
fi

# 运行快速启动
echo ""
echo "10. 运行快速启动演示..."
read -p "是否运行快速启动演示？(y/n): " run_demo

if [ "$run_demo" = "y" ] || [ "$run_demo" = "Y" ]; then
    echo "运行快速启动演示..."
    python3 quick_start.py
fi

echo ""
echo "=== 安装完成 ==="
echo ""
echo "🎉 车辆自编码识别系统安装成功！"
echo ""
echo "📖 使用方法:"
echo "1. 命令行使用:"
echo "   python -m src.cli recognize --image your_image.jpg"
echo ""
echo "2. Python API使用:"
echo "   from src.vehicle_ocr import VehicleOCR"
echo "   ocr = VehicleOCR()"
echo "   result = ocr.recognize('image.jpg')"
echo ""
echo "3. 查看帮助:"
echo "   python -m src.cli --help"
echo ""
echo "4. 运行演示:"
echo "   python quick_start.py"
echo ""
echo "📁 项目文件:"
echo "   - README.md: 项目说明"
echo "   - config.yaml: 配置文件示例"
echo "   - quick_start.py: 快速启动脚本"
echo "   - tests/: 测试文件"
echo ""

if [ "$create_venv" = "y" ] || [ "$create_venv" = "Y" ]; then
    echo "⚠️  注意: 使用前请激活虚拟环境:"
    echo "   source venv/bin/activate"
    echo ""
fi

echo "✨ 祝您使用愉快！"
