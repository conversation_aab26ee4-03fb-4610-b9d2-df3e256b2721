"""
车辆OCR系统测试
Tests for vehicle OCR system
"""

import pytest
import numpy as np
import cv2
from pathlib import Path
import tempfile
import json

from src.vehicle_ocr import VehicleOCR
from src.utils.config import Config
from src.utils.validator import ResultValidator, ValidationResult


class TestVehicleOCR:
    """车辆OCR系统测试类"""
    
    @pytest.fixture
    def config(self):
        """测试配置"""
        return Config()
    
    @pytest.fixture
    def ocr_system(self, config):
        """OCR系统实例"""
        return VehicleOCR(config)
    
    @pytest.fixture
    def sample_image(self):
        """创建测试图像"""
        # 创建一个简单的测试图像，包含数字
        image = np.ones((480, 640, 3), dtype=np.uint8) * 255
        
        # 在图像上绘制数字 "123"
        cv2.putText(image, "123", (250, 250), cv2.FONT_HERSHEY_SIMPLEX, 
                   3, (0, 0, 0), 5)
        
        return image
    
    def test_config_creation(self, config):
        """测试配置创建"""
        assert config is not None
        assert config.yolo.model_name == "yolo11n.pt"
        assert config.validation.number_range == (1, 1000)
    
    def test_ocr_system_initialization(self, ocr_system):
        """测试OCR系统初始化"""
        assert ocr_system is not None
        assert ocr_system.detector is not None
        assert ocr_system.ocr is not None
        assert ocr_system.validator is not None
        assert ocr_system.image_processor is not None
    
    def test_image_processing(self, ocr_system, sample_image):
        """测试图像处理"""
        processed = ocr_system.image_processor.preprocess(sample_image)
        assert processed is not None
        assert isinstance(processed, np.ndarray)
    
    def test_result_validation(self):
        """测试结果验证"""
        validator = ResultValidator()
        
        # 测试有效结果
        valid_result = {
            'text': '123',
            'confidence': 0.9
        }
        validation = validator.validate_ocr_result(valid_result)
        assert validation.is_valid
        assert validation.number == 123
        
        # 测试无效结果（超出范围）
        invalid_result = {
            'text': '2000',
            'confidence': 0.9
        }
        validation = validator.validate_ocr_result(invalid_result)
        assert not validation.is_valid
        
        # 测试低置信度结果
        low_conf_result = {
            'text': '123',
            'confidence': 0.1
        }
        validation = validator.validate_ocr_result(low_conf_result)
        assert not validation.is_valid
    
    def test_number_extraction(self):
        """测试数字提取"""
        validator = ResultValidator()
        
        # 测试各种文本格式
        test_cases = [
            ('123', 123),
            ('  456  ', 456),
            ('No.789', 789),
            ('abc123def', 123),
            ('12.34', 12),  # 应该提取最长的数字序列
            ('', None),
            ('abc', None),
        ]
        
        for text, expected in test_cases:
            result = validator._extract_number(text)
            assert result == expected, f"Failed for text: '{text}'"
    
    def test_confidence_calculation(self):
        """测试置信度计算"""
        validator = ResultValidator()
        
        # 测试综合置信度计算
        ocr_conf = 0.8
        det_conf = 0.9
        combined = validator.calculate_confidence_score(ocr_conf, det_conf)
        
        assert 0 <= combined <= 1
        assert combined > 0  # 应该有正值
    
    def test_text_quality_assessment(self):
        """测试文本质量评估"""
        validator = ResultValidator()
        
        # 测试不同质量的文本
        test_cases = [
            ('123', 1.0),  # 纯数字，最高质量
            ('12a', 0.8),  # 包含字母，质量较低
            ('!@#', 0.3),  # 特殊字符，质量很低
            ('', 0.0),     # 空文本，最低质量
        ]
        
        for text, min_expected in test_cases:
            quality = validator.assess_text_quality(text)
            assert 0 <= quality <= 1
            if min_expected > 0:
                assert quality >= min_expected * 0.5  # 允许一些误差
    
    def test_config_serialization(self, config):
        """测试配置序列化"""
        # 测试转换为字典
        config_dict = config.to_dict()
        assert isinstance(config_dict, dict)
        assert 'yolo' in config_dict
        assert 'ocr' in config_dict
        assert 'validation' in config_dict
        
        # 测试从字典创建配置
        new_config = Config.from_dict(config_dict)
        assert new_config.yolo.model_name == config.yolo.model_name
        assert new_config.validation.number_range == config.validation.number_range
    
    def test_config_yaml_operations(self, config):
        """测试配置YAML操作"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            temp_path = f.name
        
        try:
            # 保存配置
            config.save_yaml(temp_path)
            assert Path(temp_path).exists()
            
            # 加载配置
            loaded_config = Config.from_yaml(temp_path)
            assert loaded_config.yolo.model_name == config.yolo.model_name
            assert loaded_config.validation.number_range == config.validation.number_range
            
        finally:
            # 清理临时文件
            Path(temp_path).unlink(missing_ok=True)
    
    def test_result_creation(self, ocr_system):
        """测试结果创建"""
        # 测试成功结果
        success_result = ocr_system._create_result(
            success=True,
            number=123,
            confidence=0.9,
            text='123',
            processing_time=1.5
        )
        
        assert success_result['success'] is True
        assert success_result['number'] == 123
        assert success_result['confidence'] == 0.9
        assert success_result['text'] == '123'
        assert success_result['processing_time'] == 1.5
        assert 'timestamp' in success_result
        
        # 测试失败结果
        error_result = ocr_system._create_result(
            success=False,
            error='Test error',
            processing_time=0.5
        )
        
        assert error_result['success'] is False
        assert error_result['error'] == 'Test error'
        assert error_result['number'] is None
        assert error_result['confidence'] == 0.0
    
    def test_system_info(self, ocr_system):
        """测试系统信息获取"""
        info = ocr_system.get_system_info()
        
        assert isinstance(info, dict)
        assert 'yolo_info' in info
        assert 'ocr_info' in info
        assert 'config' in info
        
        # 检查YOLO信息
        yolo_info = info['yolo_info']
        assert 'model_name' in yolo_info
        assert 'device' in yolo_info
        
        # 检查OCR信息
        ocr_info = info['ocr_info']
        assert 'lang' in ocr_info
        assert 'use_gpu' in ocr_info


class TestImageProcessor:
    """图像处理器测试类"""
    
    @pytest.fixture
    def sample_image(self):
        """创建测试图像"""
        return np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    def test_image_resize(self, sample_image):
        """测试图像调整大小"""
        from src.utils.image_processor import ImageProcessor
        
        processor = ImageProcessor()
        resized = processor.resize_image(sample_image, (320, 240))
        
        assert resized.shape[:2] == (240, 320)
    
    def test_image_crop(self, sample_image):
        """测试图像裁剪"""
        from src.utils.image_processor import ImageProcessor
        
        processor = ImageProcessor()
        bbox = (100, 100, 300, 200)
        cropped = processor.crop_region(sample_image, bbox)
        
        expected_height = 200 - 100
        expected_width = 300 - 100
        assert cropped.shape[:2] == (expected_height, expected_width)


if __name__ == '__main__':
    pytest.main([__file__])
